import { Component, Input, OnInit } from '@angular/core';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { environment } from 'src/environments/environment';
import { DateInfoComponent } from '../../shared-components/ag-renderers/cells/date-info/date-info.component';
import { JiraIdComponent } from '../../shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import moment from 'moment';
import { ActivatedRoute } from '@angular/router';
import { FreshServiceComponent } from '../../shared-components/ag-renderers/cells/fresh-service/fresh-service.component';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';

@Component({
    selector: 'app-status-dashboard',
    templateUrl: './status-dashboard.component.html',
    styleUrls: ['./status-dashboard.component.scss'],
    standalone: false
})
/*
  It is a combination of Change Management and Executive
*/
export class StatusDashboardComponent implements OnInit {
  gridStyle: string = dashboardConstants.gridStyle;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  gridOptions = dashboardConstants.gridOptions;
  columnDefs: any;
  components: any;
  gridApi: any;
  assetURL = environment.appBaseURL;
  statusData:any[]=[];
  statusType!: string;
  statusFilterData:any[]= [];
  statusFilterForm!: FormGroup;
  constructor(private onePmService: OnepmServiceService, private loaderService: LoaderService,
     private notification: NzNotificationService,private route: ActivatedRoute,private formBuilder: UntypedFormBuilder) { }

  ngOnInit(): void {
    this.statusType = this.route.snapshot.data['status'];
    this.columnDefs = this.statusType =='ChangeManagement' ? dashboardConstants.changeManagementDashboardColDef: dashboardConstants.executiveDashboardColDef;
    this.initialForm();
    if(this.statusType =='ChangeManagement')
       this.getMasterData();
    else
      this.getStatusList('');
    // Component registry for ag-grid v30+
    this.components = {
        jiraIdRenderer: JiraIdComponent,
        dateInfoRenderer: DateInfoComponent,
        freshServiceRenderer:FreshServiceComponent
    };
  }
  /* 
    Function is used for initialize form
  */
   initialForm(): FormGroup {
      return this.statusFilterForm = this.formBuilder.group({
        status: [null, Validators.required],
      });
    }
   /**
   * Triggered when the grid is ready.
   * @param params - Grid API parameters
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
  }
  /**
   * function is to get API for status dropdown.
   */
  getMasterData() {
    this.onePmService.getMasterData().subscribe((res: any) => {
      if (res.succeeded) {
        this.statusFilterData = res.data?.jiraStatus || [];
        if(this.statusFilterData && this.statusFilterData.length > 0 ){
          this.statusFilterForm.controls['status'].setValue([this.statusFilterData[0]])
          this.getStatusList(this.statusFilterData[0]?.key);
        }
      }
      else {
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
  /**
   * function is to get API for grid.
   */
  getStatusList(status:any) {
    this.statusData = [];
     if(this.gridApi){
        setTimeout(() => {
        this.gridApi?.hideOverlay();
      }, 1);
     } 
    this.loaderService.invokeLoaderComponent(true);
    const payload={
      type:this.statusType,
      status:status
    }
    this.onePmService.getChangeManagementInfo(payload).subscribe((res: any) => {
      if (res.succeeded) {
        this.statusData = res.data;
        this.loaderService.invokeLoaderComponent(false);
      }
      else {
        this.statusData = [];
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.statusData = [];
      this.loaderService.invokeLoaderComponent(false);
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
  /**
   * Method to return the ng-multiselect dropdown settings
   * @param singleSelection 
   * @param enableCheckAll 
   * @param allowSearchFilter 
   * @param id 
   * @param name 
   */
  filterSettings(
      singleSelection: boolean,
      enableCheckAll: boolean,
      allowSearchFilter: boolean,
      id: string,
      name: string): IDropdownSettings {
      return ({
        singleSelection: singleSelection,
        idField: id,
        textField: name,
        selectAllText: 'All',
        unSelectAllText: 'All',
        itemsShowLimit: 1,
        enableCheckAll: enableCheckAll,
        allowSearchFilter: allowSearchFilter,
      });
  }
  /*
    Function for export excel
  */
  onClickExport(){
    this.loaderService.invokeLoaderComponent(true);
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet(this.statusType =='ChangeManagement'? 'Change Management Dashboard':'Executive Dashboard');
        const title = this.statusType =='ChangeManagement'? 'Change_Management_Dashboard_List':'Executive_Dashboard_List ' + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
        let headers: any = [];
        this.columnDefs.forEach((ele:any) => {
          if (ele.headerName != '') headers.push(ele.headerName);
        });
        // Add Header Row
        worksheet.addRow(headers);
        // Style Header Row
        const headerRow = worksheet.getRow(1);
        headerRow.eachCell((cell, colNumber) => {
          cell.font = { bold: true };
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
        });
        // Add Data Rows
        this.statusData.forEach((data) => {
          if(this.statusType =='ChangeManagement'){
          const row = worksheet.addRow([
              data.project,
              data.issueKey,
              '',// data.serviceTicket,
              data.requesterName,
              data.summary,
              data.developmentStage,
              data.changeManagementStatus,
              data.requirementDueDate ?this.displayExportDateFormats(data.requirementDueDate):'',
              data.analysisDueDate? this.displayExportDateFormats(data.analysisDueDate):'',
              data.developmentDueDate ? this.displayExportDateFormats(data.developmentDueDate):'',
              data.uatDueDate ? this.displayExportDateFormats(data.uatDueDate):'',
              data.productionDueDate ? this.displayExportDateFormats(data.productionDueDate):'',
            ]);
            const serviceTicketValue = data.serviceTicket;
            const serviceTicketId = serviceTicketValue?.replace(/\D/g, '');
            if (serviceTicketValue) {
              row.getCell(3).value = {
                text:  data.serviceTicket,
                hyperlink: `${environment.freshServiceUrl}${serviceTicketId}`
              };
              row.getCell(3).font = { color: { argb: 'FF0000FF' }, underline: true };
            }
          }
          else{
            worksheet.addRow([
              data.project,
              data.issueKey,
              data.serviceTicket,
              data.summary,
              data.developmentStage,
              data.requirementDueDate ?this.displayExportDateFormats(data.requirementDueDate):'',
              data.analysisDueDate? this.displayExportDateFormats(data.analysisDueDate):'',
              data.developmentDueDate ? this.displayExportDateFormats(data.developmentDueDate):'',
              data.uatDueDate ? this.displayExportDateFormats(data.uatDueDate):'',
              data.productionDueDate ? this.displayExportDateFormats(data.productionDueDate):'',
            ]);
          }
        });
        // Adjust Column Widths
        worksheet.columns.forEach((column) => {
          if(this.statusType =='ChangeManagement'){
            worksheet.getColumn(5).width = 50;
            worksheet.getColumn(7).width = 70;
          }
          else{
            worksheet.getColumn(4).width = 50;
          }  
          column.width = 30; // Adjust based on content
        });
        
        // Export Workbook
        workbook.xlsx.writeBuffer().then((data) => {
          const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          fs.saveAs(blob, title + '.xlsx');
        });
        this.loaderService.invokeLoaderComponent(false);
  }
  /*
    Function for search
  */
  onSearch(){
     if (!this.statusFilterForm.valid) {
      this.statusFilterForm.markAllAsTouched();
      this.notification.blank('', 'Please select at least one status', { nzPlacement: 'bottomLeft', nzClass: 'error' });
      return;
    }
    const selectedStatuses =this.statusFilterForm.controls['status'].value || [];
    const statusKeys = selectedStatuses.map((item: any) => item['key']);
    const reorderedStatus = [
      ...statusKeys.filter((key: string) => key === 'Active'),
      ...statusKeys.filter((key: string) => key !== 'Active'),
    ];
    const status = reorderedStatus.join(',');
    this.getStatusList(status);
  }
  /*
    Function for Clear
  */
  onClearFilter(){
    const selectedStatus = this.statusFilterForm.controls['status'].value;
    const isEqual = selectedStatus.length === 1 && selectedStatus[0].key ==='Active'
    if(!isEqual){
      let activeStatus = this.statusFilterData.filter(s=>s.key =='Active')
      this.statusFilterForm.controls['status'].setValue(activeStatus);
      this.onSearch();
    }
  }
  /*
    Function for format date
  */
  displayExportDateFormats(value: Date) {
      return moment(value).format('MM-DD-YYYY')
  }
}

