<div id="onepm-form" [formGroup]="timeLogForm">
    <div class="row mt-4">
        <div class="col-lg-2 col-md-2 section-item section-first-item custom-date-width">
            <label class="multiSelectLabel">From Date</label>
            <nz-date-picker id="forms-due-date-from" nzFormat="MM-dd-yyyy" formControlName="startDate"
                [nzDisabledDate]="disableFromDate"
                (nzOnOpenChange)="onOpenChange($event)" class="custom-input w-100 date-height">
            </nz-date-picker>
        </div>
        <div class="col-lg-2 col-md-2 section-item custom-date-width">
            <label class="multiSelectLabel">To Date</label>
            <nz-date-picker id="forms-due-date-to" nzFormat="MM-dd-yyyy" formControlName="endDate"
                [nzDisabledDate]="disableToDate"
                (nzOnOpenChange)="onOpenChange($event)" class="custom-input w-100 date-height">
            </nz-date-picker>
        </div>
        <div class="col-lg-2 col-md-2 section-item">
            <label class="multiSelectLabel ps-1">Teams</label>
            <ng-multiselect-dropdown #teams id="teams" [placeholder]="''"
                [settings]="filterSettings(false,false,false, 'key', 'name')" [data]="teamsData"
                 formControlName="teams">
            </ng-multiselect-dropdown>
        </div>
        <div class="col-lg-3 col-md-2 section-item ">
            <div class="search-btn">
                <button type="button" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary" title="Search"
                    (click)="onSearch()">
                    <i aria-hidden="true" class="fa fa-search"></i></button>
                <button type="button" id="reset-btn" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary"
                    title="Clear" (click)="onClearFilter()">
                    <img [src]="assetURL + '/assets/images/clear-search-icon.svg'" height="15px">
                </button>
            </div>
        </div>
    </div>
</div>