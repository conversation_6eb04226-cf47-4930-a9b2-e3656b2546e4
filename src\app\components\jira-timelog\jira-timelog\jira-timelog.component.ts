import { Component, OnInit } from '@angular/core';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
    selector: 'app-jira-timelog',
    templateUrl: './jira-timelog.component.html',
    styleUrls: ['./jira-timelog.component.scss'],
    standalone: false
})
/* 
  JIra Time Log Component
*/
export class JiraTimelogComponent implements OnInit {

  constructor(private onePmService: OnepmServiceService, private loaderService: LoaderService, private notification: NzNotificationService,) { }
  timeLogData: any[] = [];
  timeLogFlag: boolean = false;
  initializeFlag: boolean = true;

  ngOnInit(): void {
  }
 /**
   * Function to receive emit elements from project dashboard filter component
   * @param event containing Emit elements
 */
 getFilterChange(event: any) {  
  this.getTimeLogList(event.data);
}
/**
 * Function to call API to get time log list
 * @param data 
 */
  getTimeLogList(data:any){
    this.initializeFlag = false;
    this.timeLogFlag = false;
    this.timeLogData = [];
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getTimeLogInfo(data).subscribe((res: any) => {
      if (res.succeeded) {
        this.timeLogData = res.data;
        this.timeLogFlag = true;
        this.loaderService.invokeLoaderComponent(false);
      }
      else {
        this.timeLogData = [];
        this.timeLogFlag = true;
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
      this.timeLogData = [];
      this.timeLogFlag = true;
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
}
