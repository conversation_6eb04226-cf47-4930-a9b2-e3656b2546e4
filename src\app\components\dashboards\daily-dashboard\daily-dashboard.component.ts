import { Component, OnInit, ViewChild } from '@angular/core';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { onepmApi } from 'src/app/utils/sample-data';
import { LoaderService } from 'loader';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { DailyDashboardGridComponent } from './daily-dashboard-grid/daily-dashboard-grid.component';
import { SharedService } from 'src/app/service/shared.service';

@Component({
    selector: 'app-daily-dashboard',
    templateUrl: './daily-dashboard.component.html',
    styleUrls: ['./daily-dashboard.component.scss'],
    standalone: false
})
/* 
  Daily Dashboard Component
*/
export class DailyDashboardComponent implements OnInit {
  dailyDashboardColDef: any[] = dashboardConstants.dailydashboardColDef;
  rowData: any[] = onepmApi.dalyDashboardData;
  masterData: any = {};
  dailyList: any[] = [];
  dailyFlag: boolean = false;
  isDivVisible = false;
  @ViewChild(DailyDashboardGridComponent) dailyDashboardGrid!: DailyDashboardGridComponent;

  constructor(private onePmService: OnepmServiceService, private loaderService: LoaderService, private notification: NzNotificationService, private sharedService: SharedService) {
  }

  ngOnInit(): void {
  }

  /**
   * Method to get emitted change event from daily filter component
   * @param event 
   */
  getFilterChange(event: any) {
    let payload = {
      project: event.data.project ? event.data.project : '',
      team: event.data.team ? event.data.team : '',
      member: event.data.member ? event.data.member : '',
      workLogDate: event.data.workLogDate ? event.data.workLogDate : '',
      filterRequest: [],
    };
    this.dailyDashboardGrid.showBlankOverlay();
    this.getDailyList(payload);
  }

  /**
  * Method to  call API to get project list
*/
  getDailyList(data: any) {
    this.dailyFlag = false;
    this.dailyList = [];
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getDailyDataInfo(data).subscribe((res: any) => {
      if (res.succeeded) {
        this.dailyList = res.data;
        this.dailyFlag = true;
        this.loaderService.invokeLoaderComponent(false);
      }
      else {
        this.dailyList = [];
        this.dailyFlag = true;
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.dailyList = [];
      this.dailyFlag = true;
      this.loaderService.invokeLoaderComponent(false);
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
  /**
   * Method to show and hide legends section
   */
  toggleDiv() {
    this.isDivVisible = !this.isDivVisible;
  }
  /**
   * Method to get legends length from shared service
   * @returns 
   */
  getLegendsLength() {
    return this.sharedService?.legends.length > 0;
  }
  /**
   * Method to get legends from shared service
   * @returns 
   */
  getLegends() {
    return this.sharedService?.legends;
  }
}
