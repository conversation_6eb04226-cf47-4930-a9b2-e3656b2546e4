<div id="onepm-form">
    <form [formGroup]="projectFilterForm">
        <div class="row mt-4">
            <div class="col-lg-2 col-md-2 section-item section-first-item">
                <label class="multiSelectLabel ps-1">Project</label>
                <ng-multiselect-dropdown #project id="project" [placeholder]="''"
                    [settings]="filterSettings(false,false,true, 'key', 'value')" [data]="projectData"
                    (onSelect)="onProjectSelect($event)" formControlName="project">
                </ng-multiselect-dropdown>
            </div>
            <div class="col-lg-2 col-md-2 section-item">
                <label class="multiSelectLabel ps-1">Teams</label>
                <ng-multiselect-dropdown #teams id="teams" [placeholder]="''"
                    [settings]="filterSettings(false,false,false, 'key', 'value')" [data]="masterData.teams"
                    (onSelect)="onTeamSelect()" (onDeSelect)="onTeamSelect()" formControlName="teams">
                </ng-multiselect-dropdown>
            </div>
            <div class="col-lg-2 col-md-2 section-item">
                <label class="multiSelectLabel ps-1">Members</label>
                <ng-multiselect-dropdown #members id="members" [placeholder]="''"
                    [settings]="filterSettings(false,false,true, 'userId', 'value')" [data]="memberData"
                    formControlName="members">
                </ng-multiselect-dropdown>
            </div>
            <div class="col-lg-2 col-md-2 section-item custom-date-width">
                <label class="multiSelectLabel">Work log Date</label>
                <nz-date-picker id="forms-due-date-to" nzFormat="MM-dd-yyyy" formControlName="worklogDate"
                    (nzOnOpenChange)="onOpenChange($event)" class="custom-input w-100 date-height">
                </nz-date-picker>
            </div>
            <div class="col-lg-2 col-md-2 section-item">
                <div class="search-btn">
                    <button type="button" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary" title="Search"
                        (click)="onSearch()">
                        <i aria-hidden="true" class="fa fa-search"></i></button>
                    <button type="button" id="reset-btn" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary"
                        title="Clear" (click)="onClearFilter()">
                        <img [src]="assetURL + '/assets/images/clear-search-icon.svg'" height="15px">
                    </button>
                </div>
            </div>
            <div class="col-lg-2 col-md-2 ms-auto">
                <div class="search-btn float-end">
                    <div class="excel-btn">
                        <button type="button" class="ant-btn btn btn-success me-2" [disabled]="excelData?.length == 0"
                            title="Export" (click)="onExportDaily()">
                            <img [src]="assetURL +'/assets/images/Excel-Icon.png'">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>