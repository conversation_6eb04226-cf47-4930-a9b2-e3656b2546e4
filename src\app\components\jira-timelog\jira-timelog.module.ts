import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { JiraTimelogRoutingModule } from './jira-timelog-routing.module';
import { JiraTimelogFilterComponent } from './jira-timelog/jira-timelog-filter/jira-timelog-filter.component';
import { JiraTimelogComponent } from './jira-timelog/jira-timelog.component';
import { JiraTimelogGridComponent } from './jira-timelog/jira-timelog-grid/jira-timelog-grid.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { AgGridModule } from 'ag-grid-angular';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';


@NgModule({
  declarations: [
    JiraTimelogFilterComponent,
    JiraTimelogComponent,
    JiraTimelogGridComponent
  ],
  imports: [
    CommonModule,
    JiraTimelogRoutingModule,
    NgMultiSelectDropDownModule.forRoot(),
     AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    NzButtonModule,
    NzDatePickerModule,
  ],
  providers: [
    NzNotificationService
  ]
})
export class JiraTimelogModule { }
