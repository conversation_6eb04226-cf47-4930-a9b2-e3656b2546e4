import { HttpClient, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { onepmApi } from 'src/app/utils/sample-data';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class OnepmServiceService {

  constructor(private http: HttpClient) { }

  /**
   * API: Get Project list
   */
  getProjectList(): Observable<any> {
    return of(onepmApi.projectData);
  }

  /**
   * API: Get Master Data
   */
  getMasterData(): Observable<any> {
    let url = environment.onePmUrl + `api/Dashboard/GetMasterData`;
    return this.http.get<any>(url);
  }
  /**
   * API: Get Project Data
   */
  getProjectInfo(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Dashboard/GetJiraProjectInfo`;
    return this.http.post(url, body);
  }
   /**
   * API: Get Daily Data
   */
   getDailyDataInfo(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Dashboard/GetJiraDailyTicketInfo`;
    return this.http.post(url, body);
  }
  /**
   * API: Get PMO Data
   */
  getPMODashboardInfo(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Dashboard/GetPMODashboardInfo?Type=${body}`;
    return this.http.get<any>(url);
  }
  /**
   * API: Get KPI Data
   */
  getKPIDashboardInfo(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Dashboard/GetKPIDashboardInfo`;
    return this.http.post(url, body);
  }
   /**
   * API: Get  Data
   */
   getUtilizationInfo(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Dashboard/GetUtilizationDashboardInfo`;
    return this.http.post(url, body);
  }

  getTeamsData(): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/GetTeamsDashboardInfo`;
    return this.http.get<any>(url);    
  }

  addTeamsData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/Team/Add`;
    return this.http.post(url, body);
  }

  deleteTeamsData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/Team/Delete`;
    return this.http.post(url, body);
  }

  getTeamsMasterData(): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/GetMasterData`;
    return this.http.get(url);
  }
  addTeamMemberData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/Member/Add`;
    return this.http.post(url, body);
  }
  deleteTeamMemberData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/Member/Delete`;
    return this.http.post(url, body);
  }
  addProjectData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/Project/Add`;
    return this.http.post(url, body);
  }
  deleteProjectData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/Project/Delete`;
    return this.http.post(url, body);
  }
  updateProjectData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Teams/Project/UpdatePriority`;
    return this.http.post(url, body);
  }
  getTimeLogInfo(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/BADashboard/GetJiraTimeLogInfo`;
    return this.http.post(url, body);
  }
  getPmoEodData(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/BADashboard/GetPMO_EOD_ReportInfo`;
    return this.http.post(url, body);
  }  
  getTimeLogMasterData(): Observable<any> {
    let url = environment.onePmUrl + `api/BADashboard/GetMasterData`;
    return this.http.get(url);
  }
  getEodDataInitial(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/BADashboard/GetFullBAEODReportInitialInfo?date=${body.date}`;
    return this.http.get(url);
  }
  getEodDataRemaining(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/BADashboard/GetFullBAEODReportRemainingInfo?date=${body.date}`;
    return this.http.get(url);
  }
  getChangeManagementInfo(body: any): Observable<any> {
    let url = environment.onePmUrl + `api/Dashboard/GetManagementDashboardInfo`;
    return this.http.post(url, body);
  }
}
