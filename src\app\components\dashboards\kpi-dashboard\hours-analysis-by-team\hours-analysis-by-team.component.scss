::ng-deep {
  .planned-grid .ag-header-group-cell .ag-header-cell-comp-wrapper{
    display: flex !important;
    justify-content: center !important;
  }
}

::ng-deep .team-summary-grid .wrap-header .ag-header-cell-label {
  white-space: normal !important;
  word-wrap: break-word;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

::ng-deep .team-summary-grid .ag-header-cell {
  padding-right: 0;
  padding-left: 0;

}

::ng-deep .growth-summary-grid .wrap-header .ag-header-cell-label {
  white-space: normal !important;
  word-wrap: break-word;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

::ng-deep .planned-grid .wrap-header .ag-header-cell-label {
  white-space: normal !important;
  word-wrap: break-word;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

::ng-deep .growth-summary-grid .ag-header-cell {
  padding-right: 0;
  padding-left: 0;

}