import {
  Component,
  Input,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';


@Component({
    selector: 'app-add-project',
    templateUrl: './add-project.component.html',
    styleUrls: ['./add-project.component.scss'],
    standalone: false
})
export class AddProjectComponent implements OnInit {
  @ViewChild('modalFooterTemplate', { static: true })
  modalFooterTemplate!: TemplateRef<any>;
  @Input() data: any;
  projectForm!: FormGroup;

  constructor(
    private formBuilder: FormBuilder,
    private notification: NzNotificationService,
    private modalRef: NzModalRef<AddProjectComponent>,
    private onePmService: OnepmServiceService,
    private loaderService: LoaderService,
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }
/**
   * Function is used to initialize form 
   */
  initializeForm(): FormGroup {
    return (this.projectForm = this.formBuilder.group({
      projectName: ['', [Validators.required,Validators.pattern(/^(?!\s*$).+/)]],
    }));
  }
  /**
   * Function for submit 
   */
  onSubmit() {
    if (this.projectForm.valid) {
      let userId = localStorage.getItem('userEmail');
      let payload={
        projectName:this.projectForm.value.projectName.trim(),
        teamId:this.data.teamId,
        userId:userId
      }
      this.onePmService.addProjectData(payload).subscribe(
        (res) => {
          this.loaderService.invokeLoaderComponent(false);
          if(res.succeeded){
          this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'success' });
          this.modalRef.close(true);
          }  else {
            this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
          }
        },
        (error) => {
          this.loaderService.invokeLoaderComponent(false);
          this.modalRef.close(false);
          this.notification.blank('', error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      );
    }else {
      this.notification.blank('', 'Fill the Required Fields', { nzPlacement: 'bottomLeft', nzClass: 'error' });
    }
  }
  /**
   * Function for reset 
   */
  onReset(){
    this.resetForm();
  }
  /**
   * Function for reset form 
   */
  resetForm(): void {
    this.projectForm.reset();
  }
}
