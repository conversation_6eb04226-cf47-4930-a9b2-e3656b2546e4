import { Component, OnInit } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
    selector: 'app-description',
    templateUrl: './description.component.html',
    styleUrls: ['./description.component.scss'],
    standalone: false
})
export class DescriptionComponent implements AgRendererComponent {
  params: any;
  cellValue: any;
  stageValue: any;
  constructor() { }
  agInit(params: ICellRendererParams): void {
    this.params = params;    
    this.cellValue = this.getValueToDisplay(params);
    if(this.cellValue?.stages){
      this.stageValue = this.cellValue.stages;
    }
  }
  refresh(params: ICellRendererParams): boolean {
    this.cellValue = this.getValueToDisplay(params);
    return true;
  }
  getValueToDisplay(params: ICellRendererParams) {
    return params.valueFormatted ? params.valueFormatted : params.value;
  }

}
