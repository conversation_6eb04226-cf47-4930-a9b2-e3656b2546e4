{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"BTDashboard": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": {"base": "dist/btdashboard"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./src/styles.scss", "./node_modules/bootstrap/dist/css/bootstrap.min.css", "./src/assets/css/custom-style.css", "./node_modules/font-awesome/css/font-awesome.css", "./src/assets/styles/style-common.css"], "scripts": ["./node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "develop": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.develop.ts"}], "outputHashing": "all"}, "uat": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "outputHashing": "all"}, "prod": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "BTDashboard:build:production"}, "development": {"buildTarget": "BTDashboard:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "BTDashboard:build"}}, "test": {"builder": "@angular/build:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}