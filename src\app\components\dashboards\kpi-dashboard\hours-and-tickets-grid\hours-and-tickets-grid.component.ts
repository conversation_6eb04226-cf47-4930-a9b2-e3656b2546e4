import { Component, OnInit, Input } from '@angular/core';
import { DateInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/date-info/date-info.component';
import { DateComponent } from 'src/app/components/shared-components/ag-renderers/cells/date/date.component';
import { DescriptionComponent } from 'src/app/components/shared-components/ag-renderers/cells/description/description.component';
import { HourInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour-info/hour-info.component';
import { HourComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour/hour.component';
import { JiraIdComponent } from 'src/app/components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { NormalComponent } from 'src/app/components/shared-components/ag-renderers/cells/normal/normal.component';
import { PercentageComponent } from 'src/app/components/shared-components/ag-renderers/cells/percentage/percentage.component';
import { NormalHeaderComponent } from 'src/app/components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import * as Highcharts from 'highcharts';
import { ChartData } from 'src/app/utils/apps.interface';

@Component({
    selector: 'app-hours-and-tickets-grid',
    templateUrl: './hours-and-tickets-grid.component.html',
    styleUrls: ['./hours-and-tickets-grid.component.scss'],
    standalone: false
})
/* 
  Hours and ticket component, child of KPI Dashboard
*/
export class HoursAndTicketsGridComponent implements OnInit {
  gridStyle: string = dashboardConstants.gridStyle;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  gridOptions = dashboardConstants.gridOptions;
  columnDefs: any[] = dashboardConstants.ticketsAndHours;

  rowData: any[] = [];
  chartlabel: string[] = [];
  ticketListData: number[] = [];
  hoursListdata: number[] = [];
  pinnedBottomRowData: any[] = [];


  gridApi: any;
  isDivVisible: any;
  
  chartOptions!: Highcharts.Options;
  Highcharts: typeof Highcharts = Highcharts;
  
  @Input() gridData: any[] = [];
  @Input() chartData!: ChartData;
  @Input() hoursandCountData!: any;

  constructor() { }


  // Component registry for ag-grid v30+
  components = {
    normalHeader: NormalHeaderComponent,
    normalRenderer: NormalComponent,
    hourRenderer: HourComponent,
    descRenderer: DescriptionComponent,
    dateRenderer: DateComponent,
    percentageRenderer: PercentageComponent,
    jiraIdRenderer: JiraIdComponent,
    dateInfoRenderer: DateInfoComponent,
    hourInfoRenderer: HourInfoComponent,
  };

  ngOnInit(): void {
    this.onChartReady();
  }

  ngOnChanges() {
    this.rowData = [];
    this.pinnedBottomRowData = [];
    this.chartlabel = [];
    this.ticketListData = [];
    this.hoursListdata = [];
    if (this.gridData?.length) {
      this.rowData = this.gridData;
      this.pinnedBottomRowData = [
        { project: 'Total', ticketsCount: this.hoursandCountData.ticketsCount, totalHours: this.hoursandCountData.totalHours, percentage: this.hoursandCountData.totalPercentage }
      ];
    }
    if (this.chartData) {
      const { projects, ticketsCount, totalHours } = this.chartData;
      if (projects?.length) {
        this.chartlabel = projects;
      }
      if (totalHours?.length) {
        this.hoursListdata = totalHours;
      } if (ticketsCount?.length) {
        this.ticketListData = ticketsCount;
      }
      if (projects?.length && ticketsCount?.length && totalHours?.length) {
        this.onChartReady();
      }
    }
  }
  /**
   * AG-Grid initialization when the grid is ready.
   * @param params 
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    this.updateColumnSize(this.gridApi);
  }
  /**
   * Hides the AG-Grid overlay after a short delay.
   */
  showBlankOverlay() {
    setTimeout(() => {
      this.gridApi?.hideOverlay();
    }, 1);
  }
  /**
   * Adjusts column sizes dynamically when the window resizes.
   * @param param 
   */
  updateColumnSize(param: any) { window.addEventListener('resize', () => { setTimeout(() => param.sizeColumnsToFit(), 500) }) }
  /**
   * Configures and initializes the Highcharts chart.
   */
  onChartReady() {
    this.chartOptions = {
      chart: {
        zooming: {
          type: 'xy'
        },
        style: {
          fontFamily: 'Poppins, sans-serif' // Apply font to entire chart
        }
      },
      title: {
        text: 'Tickets and Hours by Project',
        style: {
          fontFamily: 'Poppins, sans-serif'
        }
      },
      xAxis: {
        categories: [...this.chartlabel],
        labels: {
          rotation: -60,
          style: {
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            fontFamily: 'Poppins, sans-serif'
          }
        }
      },
      yAxis: [
        {
          title: {
            text: 'Hours',
            style: {
              fontFamily: 'Poppins, sans-serif'
            }
          },
          opposite: false
        },
        {
          title: {
            text: 'Tickets',
            style: {
              fontFamily: 'Poppins, sans-serif'
            }
          },
          opposite: true
        }
      ],
      series: [
        {
          name: 'Tickets',
          type: 'column',
          data: [...this.ticketListData],
          color: '#0099FF',
          yAxis: 1
        },
        {
          name: 'Hours',
          type: 'line',
          data: [...this.hoursListdata],
          color: '#660099',
          yAxis: 0,
          marker: {
            enabled: true
          }
        }
      ],
      legend: {
        enabled: true,
        itemStyle: {
          fontFamily: 'Poppins, sans-serif'
        }
      },
      tooltip: {
        shared: true,
        style: {
          fontFamily: 'Poppins, sans-serif'
        }
      },
      credits: {
        enabled: false
      }
    };
  }

  /**
   * Toggles the visibility of a chart.
   */
  toggleDiv() {
    this.isDivVisible = !this.isDivVisible;
  }
}
