import { Component, OnInit, ViewChild } from '@angular/core';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { ProjectDashboardGridComponent } from './project-dashboard-grid/project-dashboard-grid.component';
import { SharedService } from 'src/app/service/shared.service';

@Component({
    selector: 'app-project-dashboard',
    templateUrl: './project-dashboard.component.html',
    styleUrls: ['./project-dashboard.component.scss'],
    standalone: false
})
/* 
  Project Dashboard Component
*/
export class ProjectDashboardComponent implements OnInit {
  masterData: any = [];
  projectData: any[] = [];
  isDivVisible = false;
  projectFlag: boolean = false;
  @ViewChild(ProjectDashboardGridComponent) ProjectDashboardGrid!: ProjectDashboardGridComponent;
  constructor(private onePmService: OnepmServiceService, private loaderService: LoaderService, private notification: NzNotificationService, private sharedService: SharedService) { }

  ngOnInit(): void {
  }

  /**
   * Function to  call API to get project list
   * @param data containing Parameters
 */
  getProjectList(data: any) {
    this.projectFlag = false;
    this.projectData = [];
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getProjectInfo(data).subscribe((res: any) => {
      if (res.succeeded) {
        this.projectData = res.data;
        this.projectFlag = true;
        this.loaderService.invokeLoaderComponent(false);
      }
      else {
        this.projectData = [];
        this.projectFlag = true;
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
      this.projectData = [];
      this.projectFlag = true;
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
  /**
   * Function to receive emit elements from project dashboard filter component
   * @param event containing Emit elements
 */
  getFilterChange(event: any) {
    this.getProjectList(event.data);
  }
   /**
   * Function to open legends section
  */
  toggleDiv() {
    this.isDivVisible = !this.isDivVisible;
  }
  /**
   * Function to get legends length condition
  */
  getLegendsLength() {
    return this.sharedService?.legends.length > 0;
  }
   /**
   * Function to get legends list
  */
  getLegends() {
    return this.sharedService?.legends;
  }
}
