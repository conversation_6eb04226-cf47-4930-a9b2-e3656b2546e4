<div id="onepm-form" class="row" [ngClass]="{'mt-4 ps-1': statusType == 'ChangeManagement', 'mt-1': statusType == 'Executive'}" id="onepm-form" [formGroup]="statusFilterForm">
  @if (statusType == 'ChangeManagement') {
    <div class="col-lg-2 col-md-3 section-item">
      <label class="multiSelectLabel">Status</label>
      <ng-multiselect-dropdown #status id="status" [settings]="filterSettings(false,false,true,'key','value')"
        [data]="statusFilterData"  formControlName="status" appAutoFocus>
      </ng-multiselect-dropdown>
    </div>
    <div class="col-lg-3 col-md-2 section-item">
      <div class="search-btn">
        <button type="button" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary" title="Search"
          (click)="onSearch()">
          <i aria-hidden="true" class="fa fa-search"></i></button>
          <button type="button" id="reset-btn" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary"
            title="Clear" (click)="onClearFilter()">
            <img [src]="assetURL + '/assets/images/clear-search-icon.svg'" height="15px">
          </button>
        </div>
      </div>
    }
    <div class="col-lg-3 col-md-3 justify-content-end ms-auto">
      <div class="search-btn float-end">
        <div class="excel-btn">
          <button type="button" class="ant-btn btn btn-success me-2" [disabled]="statusData && statusData.length == 0"
            title="Export" (click)="onClickExport()">
            <img [src]="assetURL +'/assets/images/Excel-Icon.png'">
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-1">
    <ag-grid-angular #agGrid [style]="gridStyle" class="ag-theme-alpine" [columnDefs]="columnDefs"
      [components]="components" [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [rowData]="statusData"
      [suppressDragLeaveHidesColumns]="true" [overlayNoRowsTemplate]="noRowsTemplate" (gridReady)="onGridReady($event)"
      [headerHeight]="25"
      [rowHeight]="20" [gridOptions]="gridOptions">
    </ag-grid-angular>
  </div>
