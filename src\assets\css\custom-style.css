body{background:#f5f6fa !important; margin:0px; font-size:15px; color:#222; font-family: 'Poppins', sans-serif;}
#onepm-form {
    padding: 0 0 0 0;
}
@media only screen and (max-width: 4000px) {
    .col-sm-custom {
        width: 11.1111% !important;
    }
    .col-sm-custom-dropdown {
        width: 14.1111% !important;
    }
}


@media only screen and (max-width: 1350px) {
    .col-sm-custom {
        width: 16.222% !important;
    }
    .col-sm-custom-dropdown {
        width: 19.1111% !important;
    }
}

@media only screen and (max-width: 981px) {
    .col-sm-custom {       
        width: 23% !important;
    }
    .col-sm-custom-dropdown {
        width: 26.1111% !important;
    }
}

@media only screen and (max-width: 767px) {
    .col-sm-custom {
        width: 33.333% !important;
    } 
    .col-sm-custom-dropdown {
        width: 36.1111% !important;
    }
}

@media only screen and (max-width: 576px) {
    .col-sm-custom {
        width: 50% !important;
    }  
    .col-sm-custom-dropdown {
        width: 53.1111% !important;
    }
}

#onepm-form .multiselect-dropdown {
    font-size: 12px !important;
    color: #212529 !important;
}

#onepm-form .multiselect-dropdown .ag-grid-custom {
    width: 100%;
    height: calc(100vh - 280px)
}

#onepm-form .multiselect-dropdown .multiselect-item-checkbox input[type=checkbox]+div:before {
    border-color: #c8c8c8 !important;
}

#onepm-form .multiselect-dropdown .multiselect-item-checkbox input[type=checkbox]:checked+div:before {
    border-color: #337ab7 !important;
}

#onepm-form .multiselect-dropdown .dropdown-btn:focus {
    border-color: #86b7fe !important;
    ;
    outline: 0;
    box-shadow: 0 0 0 .25rem #0d6efd40;
}

#onepm-form .multiselect-dropdown .dropdown-btn:hover {
    border-color: #40a9ff !important;
    border-right-width: 1px !important;
}

#onepm-form .multiselect-dropdown .multiselect-item-checkbox {
    padding: 2px 10px !important;
}

#onepm-form .multiselect-dropdown .multiselect-item-checkbox input[type=checkbox]+div:after {
    border-width: 0 0 2px 2px !important;
}

#onepm-form .multiselect-dropdown .dropdown-btn {
    border: 1px solid #ced4da !important;
    color: #818181 !important;
    font-size: 12px !important;
    padding: 8px 18px 8px 10px !important;
    background: #fff !important;
    border-radius: .375rem !important;
}

#onepm-form .multiselect-dropdown .dropdown-btn .dropdown-list {
    z-index: 3 !important;
}

#onepm-form .multiselect-dropdown .dropdown-btn .dropdown-multiselect__caret {
    line-height: unset !important;
    top: 3px;
    height: unset !important;
}

#onepm-form .multiselect-dropdown .dropdown-btn .dropdown-multiselect__caret:after {
    content: "\f107";
    font-family: "FontAwesome";
    font-weight: 900;
    display: inline-block;
    font-size: 16px;
    color: #000;
    transition: transform 0.3s ease;
    opacity: .5;
}

#onepm-form .multiselect-dropdown .dropdown-multiselect__caret::before {
    content: none !important;
}

#onepm-form .multiselect-dropdown .dropdown-btn .selected-item-container {
    width: 50% !important;
}

#onepm-form .multiselect-dropdown .dropdown-btn .selected-item-container .selected-item {
    margin-bottom: 0px !important;
    border: none !important;
    display: inline-flex;
    width: 100%;
}

#onepm-form .multiselect-dropdown .dropdown-btn .selected-item-container .selected-item span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#onepm-form .multiselect-dropdown .dropdown-btn .selected-item-container .selected-item a {
    padding-left: 5px !important;
}

#onepm-form .multiselect-dropdown .filter-textbox {
    padding: 2px 5px !important;
    height: 30px;
}

#onepm-form .multiselect-dropdown .filter-textbox input {
    padding: 0px 0 0 6px !important;
}

#onepm-form .multiselect-dropdown .dropdown-list {
    margin-top: 1px !important;
    width: 250px;
}
.ng-option-label, .no-data h5 {
    font-size: 12px !important;
}
.search-btn {
    padding-bottom: 0px !important;
    display: flex;
    margin-top: 4px;
}
.search-btn button {
    height: 33px;
    width: 38px;
    display: inline-flex !important;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}
.search-btn-pmo {
    padding-bottom: 0px !important;
    display: flex;
    margin-top: 4px;
}
.search-btn-pmo button {
    height: 33px;
    width: 38px;
    display: inline-flex !important;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}
.excel-btn {
    float: right;
    display: flex;
    margin-right: -8px;
}

.excel-btn button {
    height: 33px;
    width: 38px;
    padding: 8px;
    border-radius: 2px;
}

.excel-btn button img {
    filter: brightness(0) invert(1);
    width: 100%;
    margin-top: -2px;
}
.ag-header-cell-text{
    font-weight: normal !important;
  font-family: 'Poppins', sans-serif !important;
}
.ant-message {
    top: 80% !important;
    right: 20px;
}

.error {
    background-color: #fcc3b7 !important;
    width: 85% !important;
}

.warning {
    background-color: #ffddb5 !important;
    width: 85% !important;
}

.success {
    background-color: #baf6c0 !important;
    width: 85% !important;
}

.ant-notification-bottomLeft {
    margin-right: 0px;
    margin-left: 24px;
    left: 37% !important;
}

.ant-notification-notice-message {
    margin-bottom: 0px !important;
}

.ant-modal-confirm-body .ant-modal-confirm-content {
    margin-top: 0px !important;
}

.ant-notification-notice {
    position: relative;
    width: 450px !important;
}
.sticky-buttons-group{
    position: fixed;
    top: 70%;
    right: 0px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    transform: translateY(-50%);
    z-index: 1000;
}

.sticky-btn {
    right: 1px;
    height: 40px;
    width: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.toggle-div {
    border: 1px solid #ced4da;
    position: relative;
    padding: 4px;
    background-color: white;
    border-radius: .25rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 999;
    width: 200px;

    ul {
            max-height: calc(100dvh - 30rem);
            overflow-y: auto;
            scrollbar-width: thin;
            padding-left: 0;
            -webkit-overflow-scrolling: touch;
        }
}
.ag-header-container{
    background: #fff !important;
}
.ag-theme-alpine .ag-cell-value {
    line-height: 20px !important; 
    font-size: 12px !important;
    word-break: break-word;
}

.cell-center {
    display: flex;
    align-items: center;
    justify-content: center;
}
.cell-left {
    display: flex;
    align-items: center;
}
.ant-modal-body  ul {
    font-size: 12px !important;
    font-family: 'Poppins', sans-serif !important;
}
.worklog-list-item ul{
    padding-left: 1rem !important;
}
.ant-modal-close-icon{
 color: #fff;   
}
.ant-picker-input>input{
    font-size: 12px;
}
.ant-modal-header {
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
}
.content ul{
    margin: 0 !important;
  }
   /* Work description list   */
  .description-content ul{
      margin: 0 !important;
      padding-left: 5px !important;
      list-style: none;
  }
  .description-content ul li {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      position: relative;
      padding-left: 13px;
  }
  .description-content ul li::after {
      width: 15px;
      height: 15px;
      position: absolute;
      left: 0;
      top: 0;
      content: '\25CF';
  }
  .description-content ul li{
      display: none;
  }
  .description-content ul li:first-child{
      display: block;
  }
  .section-item{
    padding-right: 0px !important;
    padding-left: 8px !important;
}
.section-first-item{
    padding-right: 0px !important;
    padding-left: 12px !important;
}
@media screen and (max-width: 767px) {
    .section-item{
        padding-right: 12px !important;
        padding-left: 12px !important;
    }
    .search-btn button{
        margin-top: 5px !important;
    }
}
@media screen and (min-width: 768px) {
    .custom-date-width{
        max-width: 130px !important;
    }
}
.btn-success:focus{
    color: var(--bs-btn-hover-color);
    background-color: var(--bs-btn-hover-bg);
    border-color: var(--bs-btn-hover-border-color);
  }
  .ag-theme-alpine .ag-floating-bottom {
    overflow-y: hidden !important;
    font-weight: bold ;
    .ag-cell-value{
        color: #555 !important;
    } 
  }
  .header-center .ag-header-group-cell-label {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }
  .ag-header-group-text{
    color: #555;
  }
  .merged-header{
    font-weight: bold ;
    background-color: lightgray;
    color: #555;
}
.merged-total{
    background-color: #F7FFA8;
    color: #7C8900;    
}
.merged-team{  
    background-color: #CAE8E8;
    color: #306D6C;    
}
.merged-project{    
    background-color: #CBDCF8;
    color: #305394;    
}
.merged-epic{    
    background-color: #FFF3DD;
    color: #927038;    
}
.merged-team-total{
    background-color: #FFC59E;
    color: #863600; 
}
  .ag-center-header .ag-header-cell-label {
    justify-content: center;
  }
   .ag-header-cell-menu-button {
    opacity: 1 !important;
    visibility: visible !important;
  }
  .ant-btn-primary[disabled] {
  background-color: #40a9ff !important;
  color: #fff;
}
.ag-icon-filter::before {
        content: var(--ag-icon-font-code-menu, "\f11e");
}
.ag-filter-active .ag-icon-filter::before{
        content: var(--ag-icon-font-code-filter, "\f115");
}