import { Component, Input, OnInit } from '@angular/core';
import { HourComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour/hour.component';
import { NormalComponent } from 'src/app/components/shared-components/ag-renderers/cells/normal/normal.component';
import { OpportunityIdAdminComponent } from 'src/app/components/shared-components/ag-renderers/cells/opportunity-id-admin/opportunity-id-admin.component';
import { NormalHeaderComponent } from 'src/app/components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { DateComponent } from 'src/app/components/shared-components/ag-renderers/cells/date/date.component';
import { PercentageComponent } from 'src/app/components/shared-components/ag-renderers/cells/percentage/percentage.component';
import { WorklogComponent } from 'src/app/components/shared-components/ag-renderers/cells/worklog/worklog.component';
import { JiraIdComponent } from 'src/app/components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { HourInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour-info/hour-info.component';

@Component({
    selector: 'app-daily-dashboard-grid',
    templateUrl: './daily-dashboard-grid.component.html',
    styleUrls: ['./daily-dashboard-grid.component.scss'],
    standalone: false
})
/* 
  Daily Dashboard Grid Component
*/
export class DailyDashboardGridComponent implements OnInit {
  gridStyle: string = dashboardConstants.gridStyle;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  gridOptions = dashboardConstants.gridOptions;
  gridApi: any;
  columnDefs: any;
  @Input() dailydashBoardColDef: any;
  @Input() dailydashBoardRowData: any;

  // Component registry for ag-grid v30+
  components = {
    normalHeader: NormalHeaderComponent,
    normalRenderer: NormalComponent,
    hourRenderer: HourComponent,
    opportunityIdRenderer: OpportunityIdAdminComponent,
    dateRenderer: DateComponent,
    percentageRenderer: PercentageComponent,
    worklogRenderer: WorklogComponent,
    jiraIdRenderer: JiraIdComponent,
    hourInfoRenderer: HourInfoComponent,
  };

  constructor() {}

  ngOnInit(): void {
    this.columnDefs = this.dailydashBoardColDef;
  }
  /**
   * Triggered when the grid is ready.
   * @param params - Grid API parameters
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
  }
  /**
   * Hides the blank overlay after a short delay.
   * This is useful when refreshing data or clearing overlays.
   */
  showBlankOverlay() {
    setTimeout(() => {
      this.gridApi?.hideOverlay();
    }, 1);
  }
}
