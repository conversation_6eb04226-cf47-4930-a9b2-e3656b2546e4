<div (click)="handleClick(linkValue, $event)" class="copy-to-clip-board opportunity-detail-link">
  <a [routerLink]="[linkValue]" class="link" id="link-{{id}}">
    {{cellValue}}
  </a>
  <span class="copy-to-clip-board-icon" appCopyToClipboard *ngIf="enableCopy" [enableCopy]="enableCopy"
    [cellValue]="cellValue" [innerHtml]="" dynamicIcon="fa-clone" [currCellWidth]="currCellWidth">
    <i class="copy-icon fa fa-clone fa-lg" matTooltip="Copy Value"></i>
  </span>
</div>
