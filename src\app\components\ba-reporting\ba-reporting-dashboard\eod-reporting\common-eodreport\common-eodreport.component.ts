import { Component, Input, OnInit } from '@angular/core';
import { GridReadyEvent } from 'ag-grid-community';
import { DateInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/date-info/date-info.component';
import { JiraIdComponent } from 'src/app/components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { dashboardConstants } from 'src/app/utils/apps-constants';

@Component({
    selector: 'app-common-eodreport',
    templateUrl: './common-eodreport.component.html',
    styleUrls: ['./common-eodreport.component.scss'],
    standalone: false
})
/* 
  common component for BA EOD reporting, child of BA reporting Dashboard
*/
export class CommonEODReportComponent implements OnInit {
  @Input() style: any;
  @Input() class: any;
  @Input() rowData: any;
  @Input() columnDefs: any;
  @Input() defaultColDef: any;
  @Input() headerHeight: any;
  @Input() rowHeight: any;
  @Input() overlayNoRowsTemplate: any;
  gridApi:any;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  gridOptions = dashboardConstants.gridOptions;

  // Component registry for ag-grid v30+
  components = {
    jiraIdRenderer: JiraIdComponent,
    dateInfoRenderer: DateInfoComponent,
  };

  constructor() {
  }

  ngOnInit(): void {
  }
  onGridReady(params: GridReadyEvent) {
      this.gridApi = params.api;
      this.gridApi.hideOverlay();
  }
 

}
