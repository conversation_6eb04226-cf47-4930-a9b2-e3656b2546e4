import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-past-due-date-grid',
    templateUrl: './past-due-date-grid.component.html',
    styleUrls: ['./past-due-date-grid.component.scss'],
    standalone: false
})
export class PastDueDateGridComponent implements OnInit {
  @Input() rowData: any[] = [];
  @Input() noRowsTemplate!: string;
  @Input() gridStyle!: string;
  @Input() defaultColDef: any;
  @Input() components: any;
  @Input() columnDefs: any;
  @Input() gridOptions: any;
  gridApi: any;
  constructor() {}

  ngOnInit(): void {}
  /**
   * Triggered when the grid is ready.
   * @param params - Grid API parameters
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    // Note: setHeaderHeight is deprecated in AG Grid v33+, use [headerHeight] in template instead
  }
}
