import { Component, ElementRef, ViewChild } from '@angular/core';
import { IHeaderAngularComp } from 'ag-grid-angular';
import { IHeaderParams } from 'ag-grid-community';

@Component({
    selector: 'app-normal-header',
    templateUrl: './normal-header.component.html',
    styleUrls: ['./normal-header.component.scss'],
    standalone: false
})
export class NormalHeaderComponent implements IHeaderAngularComp {

  params: any;

  @ViewChild('menuButton', { read: ElementRef }) menuButton:any;

  refresh(params: IHeaderParams): boolean {
    this.params = params;
    return true
  }

  agInit(params: any): void {
    this.params = params
    params.column.userProvidedColDef.sortOrder =''
  }
  /**
   * Sort is triggered
   * @param sortingOrder
   */
  sort(sortingOrder:string){
    switch(sortingOrder){
      case "asc":
        sortingOrder = 'desc';
        break
      case "desc":
        sortingOrder = '';
        break;
      default:
        sortingOrder = 'asc'
    }
    this.params.sort(this.params.column.colDef.field,sortingOrder);
  }
  /**
   * On Menu Click
   * To trigger the filtering
   * @param event
   */
  onMenuClicked(event:any){ this.params.showColumnMenu(this.menuButton.nativeElement) }

}
