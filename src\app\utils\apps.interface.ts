export interface ChartData {
    projects: string[];
    ticketsCount: number[];
    totalHours: number[];
}
export interface Worklog {
    date: string;
    hoursWorked: number;
    tookPTO: string;
  }
  
  export interface Member {
    memberName: string;
    employeeType: string;
    numberOfEmployees: number;
    totalMTDHours: number;
    businessDaysMTD: number;
    pto: number;
    timeOff: number;
    capacity: number;
    dailyCapacity: number;
    grossCapacity: number;
    netCapacity: number;
    availableCapacity: number;
    capacityUtilization: number;
    capacityUtilizationColor: string;
    dailyWorklogs: Worklog[];
  }

  export interface TransformedMember extends Omit<Member, 'dailyWorklogs'> {
    [key: string]: number | string | undefined;
  }

  export interface UtlizationChartData {
    employeeName: string[];
    capacity: number[];
    mtdHours: any[];
}
  
export interface TeamMember {
  memberId: number;
  name: string;
  groupName: string;
  skill: string;
}

export interface TeamType {
  category: string;
  subTeamId: number;
  members: TeamMember[];
}

export interface Project {
  id: number;
  projectName: string;
  priority: number;
}

export interface Team {
  teamId: number;
  teamName : string;
  teamDescription: string;
  categoryAndMembers: TeamType[];
  projects: Project[];
}
