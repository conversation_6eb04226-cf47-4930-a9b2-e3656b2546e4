import { Injectable } from '@angular/core';
import { Workbook } from 'exceljs';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import * as fs from 'file-saver';
import { LoaderService } from 'loader';

@Injectable({
  providedIn: 'root'
})
export class KpiExportService {
  hourAnalysisGridHeader!:string;

  constructor(private loaderService: LoaderService,) { }

  exportTicketsHours(gridData: any, gridMode: string) {
    this.loaderService.invokeLoaderComponent(true);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Tickets and Hours');
    const title = gridMode + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
    let headers: any = [];
    const columnDefs = dashboardConstants.ticketsAndHours;
    columnDefs.forEach((ele) => {
      if (ele.headerName != '') headers.push(ele.headerName);
    });
    // Add Header Row
    worksheet.addRow(headers);
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    });
    const exportData =
      gridData.ticketsAndHoursByProjectResponse.tableViewInfo;
    exportData.forEach((data: any) => {
      const row = worksheet.addRow([
        data.project,
        data.ticketsCount,
        data.totalHours,
        data.percentage+` %`,
      ]);
      row.eachCell((cell) => {
        cell.alignment = { vertical: 'middle', horizontal: 'left' };
      });
    });
    worksheet.columns.forEach((column) => {
      column.width = 30; // Adjust based on content
    });
    let ticketsCount = gridData.ticketsAndHoursByProjectResponse?.ticketsCount;
    let totalHours = gridData.ticketsAndHoursByProjectResponse.totalHours;
    let totalPercentage = gridData.ticketsAndHoursByProjectResponse.totalPercentage;
    const newRow = worksheet.addRow(['Total', ticketsCount, totalHours, totalPercentage+' %']);
    newRow.eachCell((cell) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'left' };
    });
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      fs.saveAs(blob, title + '.xlsx');
    });
    this.loaderService.invokeLoaderComponent(false);
  }

  exportKeyFeatures(gridData: any, gridMode: string) {
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Key Features');
    const title = gridMode + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
    let totalHours = 0;
    worksheet.addRow(['Key Features (top 20 tickets with highest time logged per project)']);
    worksheet.getCell(1, 1).alignment = { horizontal: 'center' }
    worksheet.getCell(1, 1).font = { bold: true }
    worksheet.mergeCells(1, 1, 1, 3);
    worksheet.addRow(['Ticket', 'Description', 'Logged Hours']);
    const headerRow = worksheet.getRow(2);
    headerRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    });
    gridData.kpiKeyFeaturesResponse.forEach((data: any) => {
      totalHours += data.hours;
      if (data.isMerged) {
        const mergedRow = worksheet.addRow([data.project, null, null]);
        worksheet.mergeCells(`A${mergedRow.number}:C${mergedRow.number}`);
        mergedRow.getCell(1).alignment = { horizontal: 'left', vertical: 'middle' };
        mergedRow.font = { bold: true };
        mergedRow.eachCell((cell, colNumber) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'D3D3D3' },
          };
        });

      } else {
        worksheet.addRow([data.ticketKey, data.ticketDescription, data.hours]);
      }
    });
    const newRow = worksheet.addRow(['Total', '', totalHours]);
    newRow.eachCell((cell) => {
      cell.font = { bold: true };
    });

    worksheet.columns.forEach((column) => {
      worksheet.getColumn(2).width = 90;
      column.width = 30;
    });
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, title + '.xlsx');
    });
  }

  exportDeploymentSuccess(gridData: any, gridMode: string) {
    this.loaderService.invokeLoaderComponent(true);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('DeploymentSuccess');
    const title = gridMode + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
    let headers: any[] = [];
    headers = ['Project', 'Tickets Sent to UAT Testing', 'Tickets Passed UAT Testing', 'Tickets Failed UAT Testing']
    worksheet.addRow(['UAT Testing Success Rate']);
    worksheet.addRow(headers);
    const headerRow = worksheet.getRow(1);
    worksheet.mergeCells(1, 1, 1, 4);
    headerRow.eachCell((cell, colNumber) => {
      this.onHeaderCellHighlight(cell);
    });
    const exportData =
      gridData.kpiDeploymentSuccessResponse.kpiuatDeploymentSuccessResponse;
    exportData.forEach((data: any) => {
      worksheet.addRow([
        data.project,
        data.uatCount,
        data.passedUATCount,
        data.failedUATCount,
      ]);
    });
    worksheet.columns.forEach((column) => {
      column.width = 30; // Adjust based on content
    });
    worksheet.addRow([]);
    worksheet.addRow(['PROD  Testing Success Rate']);
    let prodHeaders: any = [];
    prodHeaders = ['Project', 'Tickets Sent to Prod Testing', 'Tickets Passed Prod Testing', 'Tickets Failed Prod Testing']
    const prodHeaderRow = worksheet.getRow(exportData?.length + 4);
    worksheet.mergeCells(exportData?.length + 4, 1, exportData?.length + 4, 4);
    prodHeaderRow.eachCell((cell, colNumber) => {
      this.onHeaderCellHighlight(cell);

    });
    worksheet.addRow(prodHeaders);
    const prodExportData = gridData.kpiDeploymentSuccessResponse.kpiprodDeploymentSuccessResponse;
    prodExportData.forEach((data: any) => {
      worksheet.addRow([
        data.project,
        data.prodCount,
        data.passedPRODCount,
        data.failedPRODCount,
      ]);
    });
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      fs.saveAs(blob, title + '.xlsx');
    });
    this.loaderService.invokeLoaderComponent(false);
  }
  onHeaderCellHighlight(cell: any) {
    cell.font = { bold: true };
    cell.alignment = { vertical: 'middle', horizontal: 'center' };
  }

  exportHoursAnalysis(gridData: any, gridMode: string) {
    this.loaderService.invokeLoaderComponent(true);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Hours Analysis By Team');
    const title = gridMode + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
    if(this.hourAnalysisGridHeader){
      worksheet.addRow([this.hourAnalysisGridHeader]);
    }else{
    worksheet.addRow(['Planned/Unplanned and RUN/GROW/TRANSFORM hours for BA, App Dev and Data Teams']);      
    }
    worksheet.mergeCells('A1:L1'); // Merge A1 to L1 for the title
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A1').font = { bold: true };

    // Add the second row for column headers
    worksheet.addRow([
      '',
      'Planned Hours',
      '',
      '',
      '',
      'Unplanned Hours',
      '',
      '',
      '',
      'Total Hours',
      '',
      ''
    ]);
    worksheet.mergeCells('B2:E2'); // "Planned Hours" header (B2 to E2)
    worksheet.mergeCells('F2:I2'); // "Unplanned Hours" header (F2 to I2)
    worksheet.mergeCells('J2:L2'); // "Total Hours" header (J2 to L2)

    // Style the second row
    worksheet.getRow(2).alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(2).font = { bold: true };

    // Add the sub-headers row
    worksheet.addRow([
      'Projects/Initiatives',
      'BA',
      'App Dev',
      'Data',
      'RPA',
      'BA',
      'App Dev',
      'Data',
      'RPA',
      'Total Planned Hours',
      'Total Unplanned Hours',
      'Total Hours',
    ]);
    worksheet.getRow(3).alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(3).font = { bold: true };
    const data = gridData?.kpiHoursAnalysisResponse?.projectSummaryResponse;
    let totalPlannedHours = 0;
    let totalUnplannedHours = 0;
    let totalHours = 0;
    data.forEach((item:any) => {
      worksheet.addRow([
        item.projectName,
        item.planned.ba,
        item.planned.appDev,
        item.planned.data,
        item.planned.rpa,
        item.unPlanned.ba,
        item.unPlanned.appDev,
        item.unPlanned.data,
        item.unPlanned.rpa,
        item.totalHours.totalPlanned,
        item.totalHours.totalUnPlanned,
        item.totalHours.totalHours,
      ]);
      totalPlannedHours += item.totalHours.totalPlanned;
      totalUnplannedHours += item.totalHours.totalUnPlanned;
      totalHours += item.totalHours.totalHours;
    });
    const mainTotal = worksheet.addRow([
      '',
      '',
      ' ',
      '',
      '',
      '',
      '',
      '',
      'Total',
      totalPlannedHours.toFixed(2),
      totalUnplannedHours.toFixed(2),
      totalHours.toFixed(2)
    ]);
    mainTotal.font = { bold: true };
    mainTotal.alignment = { horizontal: 'right', vertical: 'middle' };
    const table1 = gridData.kpiHoursAnalysisResponse.teamSummaryResponse;
    let table1Total = 0;
    // Example Data for Table 2
    const table2 = gridData.kpiHoursAnalysisResponse.teamRunGrowthTransformSummaryResponse;
    let table2Total = 0;
    
    // Example Data for JIRA Tickets
    const jiraTickets = gridData.kpiHoursAnalysisResponse.jiraTicketCountResponse;
    
    let jiraTotal = 0;
    
    let currentRow = 1;

    // Table 1
    worksheet.addRow(['']).font = { bold: true };
    currentRow++;
    const headerRow1 = worksheet.addRow(['Team', 'Planned Hours', 'Planned %', 'Unplanned Hours', 'Unplanned %', 'Total Hours']);
    headerRow1.font = { bold: true };
    headerRow1.alignment = { horizontal: 'center', vertical: 'middle' };
    table1.forEach((row:any) => {
      worksheet.addRow([row.team, row.plannedHours, row.plannedPercentage, row.unPlannedHours, row.unPlannedPercentage, row.totalHours]);
      table1Total += row.totalHours;
    });
    const total1 = worksheet.addRow(['', '', '', '', 'Total', table1Total.toFixed(2)]);
    total1.font = { bold: true };
    total1.alignment = { horizontal: 'right', vertical: 'middle' };
    currentRow += table1.length + 3;
  
    // Table 2
    worksheet.addRow(['']).font = { bold: true };
    currentRow++;
    const headerRow2 = worksheet.addRow(['Team', 'Run Hours', 'Run %', 'Growth (H)', 'Growth (%)', 'Transform (H)', 'Transform (%)', 'Total (H)']);
    headerRow2.font = { bold: true };
    headerRow2.alignment = { horizontal: 'center', vertical: 'middle' };
    table2.forEach((row:any) => {
      worksheet.addRow([
        row.team,
        row.runHours,
        row.runPercent,
        row.growthHours,
        row.growthPercent,
        row.transformHours,
        row.transformPercentage,
        row.totalHours,
      ]);
      table2Total += row.totalHours;
    });
   const total2 =  worksheet.addRow(['', '', '', '', '', '', 'Total', table2Total.toFixed(2)]);
   total2.font = { bold: true };
   total2.alignment = { horizontal: 'right', vertical: 'middle' };
    currentRow += table2.length + 3;
  
    // Table 3
    worksheet.addRow(['']).font = { bold: true };
    currentRow++;
    const headerRow3 = worksheet.addRow(['Team', 'JIRA Tickets']);
    headerRow3.font = { bold: true };
    headerRow3.alignment = { horizontal: 'center', vertical: 'middle' };
    jiraTickets.forEach((row:any) => {
      jiraTotal += row.jiraTicketCount;
      worksheet.addRow([row.team, row.jiraTicketCount]);
    });
    const total3 = worksheet.addRow(['Total', jiraTotal.toFixed(2)]);
    total3.font = { bold: true };
    total3.alignment = { horizontal: 'right', vertical: 'middle' };

    // Adjust column widths for better visibility
    worksheet.columns = [
      { width: 30 }, // Projects/Initiatives
      { width: 15 }, // BA
      { width: 15 }, // App Dev
      { width: 15 }, // Data
      { width: 15 }, // RPA
      { width: 15 }, // BA (Unplanned)
      { width: 15 }, // App Dev (Unplanned)
      { width: 15 }, // Data (Unplanned)
      { width: 15 }, // RPA (Unplanned)
      { width: 18 }, // Total Planned Hours
      { width: 20 }, // Total Unplanned Hours
      { width: 15 }, // Total Hours
    ];
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      fs.saveAs(blob, title + '.xlsx');
    });
    this.loaderService.invokeLoaderComponent(false);
  }
}
