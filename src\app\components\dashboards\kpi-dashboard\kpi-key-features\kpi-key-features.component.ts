import { Component, Input, OnInit } from '@angular/core';
import { DateInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/date-info/date-info.component';
import { DateComponent } from 'src/app/components/shared-components/ag-renderers/cells/date/date.component';
import { DescriptionComponent } from 'src/app/components/shared-components/ag-renderers/cells/description/description.component';
import { HourInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour-info/hour-info.component';
import { HourComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour/hour.component';
import { JiraIdComponent } from 'src/app/components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { NormalComponent } from 'src/app/components/shared-components/ag-renderers/cells/normal/normal.component';
import { PercentageComponent } from 'src/app/components/shared-components/ag-renderers/cells/percentage/percentage.component';
import { NormalHeaderComponent } from 'src/app/components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { onepmApi } from 'src/app/utils/sample-data';

@Component({
    selector: 'app-kpi-key-features',
    templateUrl: './kpi-key-features.component.html',
    styleUrls: ['./kpi-key-features.component.scss'],
    standalone: false
})
/* 
  KPI Key Features component, child of KPI Dashboard
*/
export class KpiKeyFeaturesComponent implements OnInit {

  gridApi: any;
  gridStyle: string = dashboardConstants.gridStyle;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  gridOptions = dashboardConstants.gridOptions;
  columnDefs: any[] = dashboardConstants.kpiKeyFeatures;
  totalData: any[] = []
  @Input() gridData: any;
  @Input() footerDetails: number = 0;

  // Component registry for ag-grid v30+
  components = {
    normalHeader: NormalHeaderComponent,
    normalRenderer: NormalComponent,
    hourRenderer: HourComponent,
    descRenderer: DescriptionComponent,
    dateRenderer: DateComponent,
    percentageRenderer: PercentageComponent,
    jiraIdRenderer: JiraIdComponent,
    dateInfoRenderer: DateInfoComponent,
    hourInfoRenderer: HourInfoComponent,
  };

  constructor() {}

  ngOnInit(): void {

  }
  ngOnChanges() {
    this.totalData = [
      { ticketKey: 'Total', hours: this.footerDetails?.toFixed(2) }
    ];
  }
   /**
   * AG-Grid initialization when the grid is ready.
   * @param params 
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    this.updateColumnSize(this.gridApi);
  }
  /**
   * Hides the AG-Grid overlay after a short delay.
   */
  showBlankOverlay() {
    setTimeout(() => {
      this.gridApi?.hideOverlay();
    }, 1);
  }
  /**
   * Adjusts column sizes dynamically when the window resizes.
   * @param param 
   */
  updateColumnSize(param: any) { window.addEventListener('resize', () => { setTimeout(() => param.sizeColumnsToFit(), 500) }) }

}
