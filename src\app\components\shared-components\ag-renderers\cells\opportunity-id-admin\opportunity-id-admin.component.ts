import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
    selector: 'app-opportunity-id-admin',
    templateUrl: './opportunity-id-admin.component.html',
    styleUrls: ['./opportunity-id-admin.component.scss'],
    standalone: false
})
export class OpportunityIdAdminComponent implements AgRendererComponent {

  enableCopy:boolean = false;
  currCellWidth:any;
  linkValue:string = "";
  cellValue:any;
  id:any;
  constructor(private router: Router) {}

  agInit(params: ICellRendererParams): void {
    let colDef:any = params.colDef
    this.enableCopy = (colDef?.enableCopy)? true : false;
    this.id = params.data.opId
    this.cellValue = this.getValueToDisplay(params);
  }

  refresh(params: ICellRendererParams):boolean {
    this.cellValue = this.getValueToDisplay(params);
    return true
  }
  /**
   * Get Value To Display
   * @param params
   * @returns
   */
  getValueToDisplay(params: ICellRendererParams) { return params.valueFormatted ? params.valueFormatted : params.value; }

  handleClick(linkValue: string, event: MouseEvent): void {
    if (!(event.target as HTMLElement).closest('span')) {
      this.router.navigateByUrl(linkValue); // Navigate to the desired URL
    }
  }
  /**
   * Set Cell Width
   * @param params
   */
  private setCellWidth(params: any): void { this.currCellWidth = params.column.actualWidth - 10; }
}
