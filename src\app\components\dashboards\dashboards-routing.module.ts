import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard.component';
import { ProjectDashboardComponent } from './project-dashboard/project-dashboard.component';
import { DailyDashboardComponent } from './daily-dashboard/daily-dashboard.component';
import { UnauthorizedComponent } from '../shared-components/unauthorized/unauthorized.component';
import { AuthGuard } from 'src/app/guard/auth.guard';
import { PmoDashboardComponent } from './pmo-dashboard/pmo-dashboard.component';
import { KpiDashboardComponent } from './kpi-dashboard/kpi-dashboard.component';
import { UltilizationDashboardComponent } from './ultilization-dashboard/ultilization-dashboard.component';
import { TeamsDashboardComponent } from './teams-dashboard/teams-dashboard.component';
import { StatusDashboardComponent } from './status-dashboard/status-dashboard.component';

const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
    children: [
      // {path: '',redirectTo: 'daily-dashboard',pathMatch: 'full'},
      { path: 'project-dashboard', component: ProjectDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'Project_Level_Report' }, canActivate: [AuthGuard] },
      { path: 'daily-dashboard', component: DailyDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'Daily_Status_Report' }, canActivate: [AuthGuard] },
      { path: 'pmo-dashboard', component: PmoDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'PMO_Dashboard' }, canActivate: [AuthGuard] },
      { path: 'kpi-dashboard', component: KpiDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'KPI_Dashboard' }, canActivate: [AuthGuard] },
      { path: 'utilization-dashboard', component: UltilizationDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'Utilization_Dashboard' }, canActivate: [AuthGuard] },
      { path: 'teams-dashboard', component: TeamsDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'Teams_Dashboard' }, canActivate: [AuthGuard] },
      { path: 'change-management-dashboard', component: StatusDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'Change_Management_Dashboard',status:'ChangeManagement' }, canActivate: [AuthGuard] },
      { path: 'executive-dashboard', component: StatusDashboardComponent, data: { capabilityGroupName: 'App_Page', capabilityName: 'Executive_Dashboard',status:'Executive' }, canActivate: [AuthGuard] },
      { path: 'unauthorized', component: UnauthorizedComponent },
      // { path: '**', redirectTo: 'daily-dashboard' }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DashboardsRoutingModule { }
