<div id="onepm-form" [formGroup]="projectFilterForm">
    <div class="row mt-4">
        <div class="col-lg-2 col-md-2 section-item section-first-item custom-date-width">
            <label class="multiSelectLabel">From Date</label>
            <nz-date-picker id="forms-due-date-from" nzFormat="MM-dd-yyyy" formControlName="startDate" [nzDisabledDate]="disableFromDate"
                (nzOnOpenChange)="onOpenChange($event)" class="custom-input w-100 date-height">
            </nz-date-picker>
        </div>
        <div class="col-lg-2 col-md-2 section-item custom-date-width">
            <label class="multiSelectLabel">To Date</label>
            <nz-date-picker id="forms-due-date-to" nzFormat="MM-dd-yyyy" formControlName="endDate" [nzDisabledDate]="disableToDate"
                (nzOnOpenChange)="onOpenChange($event)" class="custom-input w-100 date-height">
            </nz-date-picker>
        </div>
        <div class="col-lg-2 col-md-3 section-item">
            <label class="multiSelectLabel">Project</label>
            <ng-multiselect-dropdown #project id="project" [settings]="filterSettings(false,false,true,'key','value')"
                [data]="projectData" (onSelect)="onItemSelect($event)" formControlName="project" appAutoFocus>
            </ng-multiselect-dropdown>
        </div>
        <div class="col-lg-3 col-md-2 section-item ">
            <div class="search-btn">
                <button type="button" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary" title="Search"
                    (click)="onSearch()">
                    <i aria-hidden="true" class="fa fa-search"></i></button>
                <button type="button" id="reset-btn" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary"
                    title="Clear" (click)="onClearFilter()">
                    <img [src]="assetURL + '/assets/images/clear-search-icon.svg'" height="15px">
                </button>
            </div>
        </div>
        <div class="col-lg-3 col-md-3 justify-content-end ms-auto">
            <div class="search-btn float-end">
                <div class="excel-btn">
                    <button type="button" class="ant-btn btn btn-success me-2" [disabled]="excelData?.length == 0"
                        title="Export" (click)="onExportProject()">
                        <img [src]="assetURL +'/assets/images/Excel-Icon.png'">
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>