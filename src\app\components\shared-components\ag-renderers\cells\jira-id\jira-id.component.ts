import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-jira-id',
    templateUrl: './jira-id.component.html',
    styleUrls: ['./jira-id.component.scss'],
    standalone: false
})
export class JiraIdComponent implements AgRendererComponent {
  linkValue:string = "";
  cellValue:any;
  id:any;
  enableCopy:boolean = false;
  currCellWidth:any;

  constructor(private router: Router) {}

  agInit(params: ICellRendererParams): void {
    let colDef:any = params.colDef
    this.enableCopy = (colDef?.enableCopy)? true : false;
    this.cellValue = this.getValueToDisplay(params)
    this.id = params.data.opId
    this.linkValue = `${environment.jiraUrl}${this.cellValue}`;    
  }

  refresh(params: ICellRendererParams):boolean {
    this.cellValue = this.getValueToDisplay(params);
    return true
  }
  handleClick(event:any){
    window.open(this.linkValue, '_blank');
  }
  getValueToDisplay(params: ICellRendererParams) { return params.valueFormatted ? params.valueFormatted : params.value; }

}
