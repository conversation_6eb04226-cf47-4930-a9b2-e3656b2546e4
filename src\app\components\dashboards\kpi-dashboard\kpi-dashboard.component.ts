import { Component, OnInit, ViewChild } from '@angular/core';
import { HoursAndTicketsGridComponent } from './hours-and-tickets-grid/hours-and-tickets-grid.component';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';

@Component({
    selector: 'app-kpi-dashboard',
    templateUrl: './kpi-dashboard.component.html',
    styleUrls: ['./kpi-dashboard.component.scss'],
    standalone: false
})
/*
  It is Parent component, It is a combination of Hours analysis, Hours and tickets,key features and Deployment Success
*/
export class KpiDashboardComponent implements OnInit {
  @ViewChild(HoursAndTicketsGridComponent)
  hoursDashboardGrid!: HoursAndTicketsGridComponent;
  ticketHoursGridData: any[] = [];
  keyFeatureGridData: any[] = [];
  uatSuccessGridData: any[] = [];
  prodSuccessGridData: any[] = [];
  projectSummaryGrid: any[] = [];
  teamSummaryGrid: any[] = [];
  growthSummaryGrid: any[] = [];
  jiraSummaryGridData: any[] = [];
  hoursChartData: any = {};
  hoursandCountData: any;
  gridData: any;
  gridMode: string = '';
  fromDate: string = '';
  toDate: string = '';
  keyFeatureloggedData!: number;
  constructor(private onePmService: OnepmServiceService, private loaderService: LoaderService) { }

  ngOnInit(): void { }
  /**
   * Handles filter changes and updates the grid data accordingly.
   * @param event Contains filter details and whether to clear filters.
   */
  getFilterChange(event: any) {
    if(event.clearFilter) {
      this.gridMode ='';
      return
    }
    if (this.hoursDashboardGrid) this.hoursDashboardGrid.showBlankOverlay();
    this.getKpiList(event.data);
    this.gridMode = event.data.type;
    this.fromDate = this.convertDateFormat(event.data.fromDate);
    this.toDate = this.convertDateFormat(event.data.toDate);
  }
  /**
   * Converts a date string from "YYYY-MM-DD" format to "MM/DD/YYYY".
   * @param dateStr The date string to convert.
   * @returns The formatted date string.
   */
  convertDateFormat(dateStr: string): string {
    if (!dateStr) return ''; // Handle empty or invalid input
    const [year, month, day] = dateStr.split('-');
    return `${month}/${day}/${year}`;
  }
  /**
   * Fetches KPI data based on the provided filter criteria.
   * @param data The filter criteria used to fetch KPI data.
   */
  getKpiList(data: any) {
    this.loaderService.invokeLoaderComponent(true);

    this.onePmService.getKPIDashboardInfo(data).subscribe((res: any) => {
      this.loaderService.invokeLoaderComponent(false);
      this.gridData = res.data;

      switch (data.type) {
        case 'Tickets_And_Hours_By_Project':
          this.ticketHoursGridData = res.data.ticketsAndHoursByProjectResponse.tableViewInfo;
          this.hoursChartData = res.data.ticketsAndHoursByProjectResponse.chartViewInfo;
          this.hoursandCountData = res.data.ticketsAndHoursByProjectResponse;
          break;

        case 'Key_Features':
          this.keyFeatureGridData = res.data.kpiKeyFeaturesResponse;
          this.keyFeatureloggedData = this.keyFeatureGridData?.reduce((sum: number, row: any) => sum + row.hours, 0);
          break;

        case 'Deployment_Success':
          this.uatSuccessGridData = res.data.kpiDeploymentSuccessResponse.kpiuatDeploymentSuccessResponse;
          this.prodSuccessGridData = res.data.kpiDeploymentSuccessResponse.kpiprodDeploymentSuccessResponse;
          break;

        case 'Hours_Analysis':
          this.projectSummaryGrid = res.data.kpiHoursAnalysisResponse.projectSummaryResponse;
          this.teamSummaryGrid = res.data.kpiHoursAnalysisResponse.teamSummaryResponse;
          this.growthSummaryGrid = res.data.kpiHoursAnalysisResponse.teamRunGrowthTransformSummaryResponse;
          this.jiraSummaryGridData = res.data.kpiHoursAnalysisResponse.jiraTicketCountResponse;
          break;
      }
    });
  }
}
