import { Component, Input, OnInit } from '@angular/core';
import { dashboardConstants } from 'src/app/utils/apps-constants';

@Component({
    selector: 'app-eod-reporting',
    templateUrl: './eod-reporting.component.html',
    styleUrls: ['./eod-reporting.component.scss'],
    standalone: false
})
/* 
  Component for BA EOD Reporting, child of BA Reporting Dashboard
*/
export class EodReportingComponent implements OnInit {
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  applicationBugColDef = dashboardConstants.applicationBugColDef;
  changeManagementColDef = dashboardConstants.changeManagementColDef;
  salesforceColDef = dashboardConstants.salesforceDevelopPriorityColDef;
  salesForceSpportColDef = dashboardConstants.salesforceSupportPriorityColDef;
  oneButtonDevPrioritiesColdef = dashboardConstants.oneButtonDevPrioritiesColdef;
  baStatusColDef = dashboardConstants.baStatusColDef;
  frameworkComponents: any;
  @Input() isApplicationBugLoader:boolean = true;
  @Input() finalLoader:boolean = true;
  @Input() oneDrawColDef:any[]=[];
  @Input() allApplicationBugs:any[] = []
  @Input() activeChangeManagementRequests:any[] =[];  
  @Input() salesforceCases:any[] = [];
  @Input() salesForceSupportCases:any[] = [];  
  @Input() oneButtonTotal:any;
  @Input() oneDrawTotal:any;
  @Input() baStatusUpdateData:any;
  @Input() currentlyOpenCases:any[] = [];
  @Input() currentlyOpenCasesColDef:any[]=[];  
  @Input() closedCases:any[] = [];
  @Input() closedColDef:any[]=[];
  @Input() openedTodayCases:any[] = [];
  @Input() openedTodayColDef:any[]=[];  
  @Input() oneButtonPriorityData:any[] = [];
  constructor() {}
 
  ngOnInit(): void { } 
}