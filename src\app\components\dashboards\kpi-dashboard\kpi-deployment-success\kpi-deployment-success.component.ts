import { Component, Input, OnInit } from '@angular/core';
import { DateInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/date-info/date-info.component';
import { DateComponent } from 'src/app/components/shared-components/ag-renderers/cells/date/date.component';
import { DescriptionComponent } from 'src/app/components/shared-components/ag-renderers/cells/description/description.component';
import { HourInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour-info/hour-info.component';
import { HourComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour/hour.component';
import { JiraIdComponent } from 'src/app/components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { NormalComponent } from 'src/app/components/shared-components/ag-renderers/cells/normal/normal.component';
import { PercentageComponent } from 'src/app/components/shared-components/ag-renderers/cells/percentage/percentage.component';
import { NormalHeaderComponent } from 'src/app/components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { dashboardConstants } from 'src/app/utils/apps-constants';

@Component({
    selector: 'app-kpi-deployment-success',
    templateUrl: './kpi-deployment-success.component.html',
    styleUrls: ['./kpi-deployment-success.component.scss'],
    standalone: false
})
/* 
  deployment success component, child of KPI Dashboard
*/
export class KpiDeploymentSuccessComponent implements OnInit {

  gridApi: any;
  gridApi2: any;
  frameworkComponents: any;
  gridStyle: string = 'height: calc(100vh - 200px) !important; width: 100%;';

  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  gridOptions = dashboardConstants.gridOptions;
  kpiUATSuccessColumnDefs: any[] = dashboardConstants.kpiUATSuccess;
  kpiProdSuccessColumnDefs: any[] = dashboardConstants.kpiProdSuccess;

  @Input() kpiUatSuccessData: any[] = [];
  @Input() kpiProdSuccessData: any[] = [];

  constructor() {
    this.frameworkComponents = {
      normalHeader: NormalHeaderComponent,
      normalRenderer: NormalComponent,
      hourRenderer: HourComponent,
      descRenderer: DescriptionComponent,
      dateRenderer: DateComponent,
      percentageRenderer: PercentageComponent,
      jiraIdRenderer: JiraIdComponent,
      dateInfoRenderer: DateInfoComponent,
      hourInfoRenderer: HourInfoComponent,
    }
  }

  ngOnInit(): void {
  }
  /**
   * This method is called when the UAT success grid is ready.
   * It initializes the grid API and updates the column sizes.
   * @param params - The grid parameters passed from ag-Grid.
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    this.updateColumnSize(this.gridApi);
  }
  /**
   * This method is called when the production success grid is ready.
   * It initializes the second grid API and updates the column sizes.
   * @param params - The grid parameters passed from ag-Grid.
   */
  onGridReadyprod(params: any) {
    this.gridApi2 = params.api;
    this.gridApi2.hideOverlay();
    this.updateColumnSize(this.gridApi2);
  }
  /**
   * Hides the blank overlay after a short delay.
   * This is useful when refreshing data or clearing overlays.
   */
  showBlankOverlay() {
    setTimeout(() => {
      this.gridApi?.hideOverlay();
      this.gridApi2?.hideOverlay();
    }, 1);
  }
  /**
   * Adjusts column sizes dynamically when the window resizes.
   * @param param 
   */
  updateColumnSize(param: any) { window.addEventListener('resize', () => { setTimeout(() => param.sizeColumnsToFit(), 500) }) }

}
