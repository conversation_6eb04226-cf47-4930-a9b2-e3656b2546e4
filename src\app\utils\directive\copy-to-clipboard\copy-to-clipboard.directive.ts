import { Directive, ElementRef, HostListener, Input} from '@angular/core';
import { SharedService } from 'src/app/service/shared.service';

@Directive({
    selector: '[appCopyToClipboard]',
    standalone: false
})
export class CopyToClipboardDirective {
  @Input() enableCopy: any;
  @Input() cellValue: any;
  @Input() currCellWidth: any;
  @Input() innerHtml: any;

  @HostListener('tap', ['$event'])
  @HostListener('click', ['$event'])
  onClick(event: any) {
    if (this.enableCopy && event.target.classList.value.includes('copy-icon')) {
      event.preventDefault();
      this.copyToClipboard();
    }
  }

  constructor(
    private el: ElementRef,
    private sharedService: SharedService
  ) {}

  /**
   * Copy the selected text to clipboard
   */
  copyToClipboard(): void {
    const textToCopy = this.cellValue;
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        let previousElement = this.sharedService.selectedElement;

        if (previousElement) {
          this.changeElementState(previousElement, 'fa-clone');
        }
        this.sharedService.selectedElement = this.el.nativeElement;
        this.changeElementState(this.el.nativeElement, 'fa-check-circle');
      })
      .catch((err) => console.error('Unable to copy text', err));
  }

  /**
   * Function to change the copy icon of the selected element
   * @param element the HTMLElement to change the text icon
   * @param icon the desired icon to be replaced
   */
  changeElementState(element: HTMLElement, icon: string) {
    let title = 'Copied';
    if (icon === 'fa-clone') title = 'Copy Value';
    const iconElement: any = element.querySelector('.copy-icon');
    if (iconElement) {
      iconElement.className = `copy-icon fa fa-lg ${icon}`;
    }
  }
}
