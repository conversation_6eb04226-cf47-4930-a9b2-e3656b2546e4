import { Component, ViewChild } from '@angular/core';
import { MsalService } from '@azure/msal-angular';
import { EventType } from '@azure/msal-browser';
import { AuthService } from './service/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { LoaderService } from 'loader';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { HeaderComponent } from './components/shared-components/header/header.component';


@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    standalone: false
})
export class AppComponent {
  title = 'BTDashboard';
  userName: string | null = '';
  @ViewChild(HeaderComponent) headerComponent!: HeaderComponent;

  constructor(
    private msalService: MsalService,
    private authService: AuthService, private router: Router, private route: ActivatedRoute,
    private loaderService: LoaderService,
    private notification: NzNotificationService) { }

  async ngOnInit(): Promise<void> {
    this.loaderService.invokeLoaderComponent(true);

    // Initialize MSAL instance before using it
    try {
      await this.msalService.instance.initialize();
      this.checkAccountStatus();
    } catch (error) {
      console.error('MSAL initialization failed:', error);
      this.loaderService.invokeLoaderComponent(false);
    }
  }

  checkAccountStatus() {
    const accounts = this.msalService.instance.getAllAccounts();
    if (accounts.length > 0) {
      localStorage.setItem('userName', String(this.msalService.instance.getActiveAccount()?.name));
      localStorage.setItem('userEmail', String(this.msalService.instance.getActiveAccount()?.username));      
      this.msalService.instance.setActiveAccount(accounts[0]);
      this.userName = localStorage.getItem('userName');
      this.authenticate();

    } else {
      this.msalService.instance.addEventCallback((event: any) => {
        if (event.eventType === EventType.LOGIN_SUCCESS && event.payload.account) {
          const account = event.payload.account;
          this.msalService.instance.setActiveAccount(account);
        }
      });

      this.msalService.instance.handleRedirectPromise().then((res => {
        const account = this.msalService.instance.getActiveAccount();
        if (!account) {
          this.msalService.loginRedirect();
        }
        else {
          if (res != null && res.account != null) {
            this.msalService.instance.setActiveAccount(res.account);
            localStorage.setItem('userName', String(this.msalService.instance.getActiveAccount()?.name));
            localStorage.setItem('userEmail', String(this.msalService.instance.getActiveAccount()?.username)); 
            this.userName = localStorage.getItem('userName')
            this.authenticate();
          } else {
            this.msalService.loginRedirect();
          }
        }
      })).catch(err => {
        console.error(err);
      });
    }
  }

  /**
   * Authenticate Capabilities and Roles
   */
  authenticate() {
    this.loaderService.invokeLoaderComponent(true);
    this.authService.authenticate().subscribe({
      next: (res) => {
        this.loaderService.invokeLoaderComponent(false);
        if (res.HasEntitlement) {
          localStorage.setItem('OnePmRole', res.application_role_name);
          localStorage.setItem('OnePmExternalUser', res.is_external);
          this.authService.onePMCapaability = res.capabilities;
          this.updateCapability(res.capabilities);
          this.headerComponent.isLoggedIn();
        } else {
          this.authService.onePMCapaability = [];
          this.router.navigate(['/unauthorized']);
          this.headerComponent.isLoggedIn();
        }
      },
      error: (err) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        //  this.authenticate();    
      }
    });
  }
  /**
  * Update Capabilities in the respective services
  * @param capabilities
  */
  updateCapability(capabilities: any) {
    let capability: any[] = []
    if (capabilities.App_Page != undefined) capabilities.App_Page.forEach((element: any) => capability.push(element));
    this.authService.setCapabilities(capabilities);
  }

  /**
   * Logout
   * @param $event
   */
  logout(): void {
    const activeAccount = this.msalService.instance.getActiveAccount();
    if (activeAccount) {
      localStorage.clear();
      this.msalService.logout();
    } else {
      console.warn("No active account to logout.");
    }
  }
}
