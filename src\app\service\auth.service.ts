import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, map, Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  onePMCapaability:any;
  capabilitiesSubject = new BehaviorSubject<any>(null);
capabilitiesObservable = this.capabilitiesSubject.asObservable();
  capbilityUpdated:boolean = false;
  appCapabilities:any[] = [];

  constructor(private http: HttpClient) { }

  authenticate():Observable<any>{
    let url = environment.authUrl + `Capability/getUserCapability/${environment.appDetails.appId}`;
    return this.http.get<any>(url);

  }

  setCapabilities(capabilities: any) {
    this.onePMCapaability = capabilities;
    this.capabilitiesSubject.next(capabilities);
    this.capabilitiesSubject.complete()
  }
  

  haveViewAccess(capabilityGroupName: string, capabilityName: string): boolean {
    // Check if onePMCapaability is defined and not empty
    if (!this.onePMCapaability || Object.keys(this.onePMCapaability).length === 0) {
      return false;
    }
  
    const capabilitiesGroup: any = this.onePMCapaability[capabilityGroupName];
    
    if (!capabilitiesGroup) {
      return false; // Capability group not found
    }
  
    return capabilitiesGroup.some((ele: any) =>
      ele.capability_name === capabilityName &&
      (ele.capability_value === 'Operation_Allowed')
    );
  }

}
