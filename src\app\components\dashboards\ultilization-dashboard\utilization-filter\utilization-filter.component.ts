import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { LoaderService } from 'loader';
import moment from 'moment';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { SharedService } from 'src/app/service/shared.service';
import { environment } from 'src/environments/environment';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';

@Component({
    selector: 'app-utilization-filter',
    templateUrl: './utilization-filter.component.html',
    styleUrls: ['./utilization-filter.component.scss'],
    standalone: false
})
/* 
  Component for Utilization Filter, child of Utilization Dashboard
*/
export class UtilizationFilterComponent implements OnInit {
  assetURL = environment.appBaseURL;
  filterForm!: FormGroup;
   viewData: any[] = [];
   @Output() emitFilters = new EventEmitter<any>();
   @Input() excelData: any;
   @Input() columnDefs: any;
   constructor(
     private formBuilder: UntypedFormBuilder,
     private notification: NzNotificationService,
     private loaderService: LoaderService,
     private onePmService: OnepmServiceService,
   ) { }
 
   ngOnInit(): void {
     this.initializeForm();
     this.getMasterData();
   }
   /**
    * Initializes the KPI filter form with required fields.
    * @returns FormGroup instance with view, startDate, and endDate fields.
    */
   initializeForm(): FormGroup {
     return (this.filterForm = this.formBuilder.group({
       view: [null, Validators.required],
       startDate: [this.getCurrentWeekDates().monday, Validators.required],
       endDate: [this.getCurrentWeekDates().friday, Validators.required],
     }));
   }
   /**
  * Function to get Current week date (monday and friday for initializing default)
 */
  getCurrentWeekDates(): { monday: Date; friday: Date } {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const diffToFriday = dayOfWeek === 0 ? -2 : 5 - dayOfWeek;
    const monday = new Date(today);
    const friday = new Date(today);
    monday.setDate(today.getDate() + diffToMonday);
    friday.setDate(today.getDate() + diffToFriday);
    return { monday, friday };
  }
   /**
    * Handles the search action.
    * Emits selected filter data if the form is valid, otherwise shows an error notification.
    */
   onSearch() {
     if (this.filterForm.valid) {
       const startDate = this.changeDateFormats(
         this.filterForm.controls['startDate'].value
       );
       const endDate = this.changeDateFormats(
         this.filterForm.controls['endDate'].value
       );
       const selectedItem = this.filterForm.controls['view'].value;
       const payloadData = {
        groupName: selectedItem ? selectedItem : null,
        fromDate: startDate,
        toDate: endDate,
       };      
       this.emitFilters.emit({ data: payloadData });
     } else {
      if(!this.filterForm.controls['view'].value || this.filterForm.controls['view'].value.length === 0){
        this.notification.blank('', 'Please select Group', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
       if(!this.filterForm.controls['startDate'].value){
        this.notification.blank('', 'Please select From Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      if(!this.filterForm.controls['endDate'].value){
        this.notification.blank('', 'Please select To Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      
     }
   }
   /**
    * Converts a date object to the format 'YYYY-MM-DD'.
    * @param value Date object to be formatted.
    * @returns Formatted date string.
    */
   changeDateFormats(value: Date) {
     return moment(value).format('YYYY-MM-DD');
   }
   /**
    * Disables dates in the 'To Date' picker before the selected 'From Date'.
    * @param current Date to be checked.
    * @returns Boolean indicating whether the date should be disabled.
    */
   disableToDate = (current: Date): boolean => {
     const fromDate = this.filterForm.get('startDate')?.value;
     return fromDate ? moment(current).isBefore(moment(fromDate), 'day') : false;
   };
    /**
    * Disables dates in the 'From Date' picker after the selected 'To Date'.
    * @param current Date to be checked.
    * @returns Boolean indicating whether the date should be disabled.
    */
   disableFromDate = (current: Date): boolean => {
     const toDate = this.filterForm.get('endDate')?.value;
     return toDate ? moment(current).isAfter(moment(toDate), 'day') : false;
   };
   /**
    * Fetches master data for KPI filter dropdown and handles the API response.
    * Shows a loader while fetching data and displays a notification in case of an error.
    */
   getMasterData() {    
     this.loaderService.invokeLoaderComponent(true);
     this.onePmService.getMasterData().subscribe(
       (res: any) => {
         if (res.succeeded) {
           this.loaderService.invokeLoaderComponent(false);
           this.viewData = res.data.group;
         } else {
           this.loaderService.invokeLoaderComponent(false);
           this.notification.blank('', res.message, {
             nzPlacement: 'bottomLeft',
             nzClass: 'error',
           });
         }
       },
       (err) => {
         this.loaderService.invokeLoaderComponent(false);
         this.notification.blank('', err.message, {
           nzPlacement: 'bottomLeft',
           nzClass: 'error',
         });
       }
     );
   }
   /**
    * Clears the form and emits an event to indicate filter reset.
    */
   onClear() {
     this.filterForm.reset();
    //  this.emitFilters.emit({ clearFilter: true });
   }
   /**
    * Exports Utlization data based on the selected grid mode.
    * Calls the appropriate export method from `KpiExportService`.
    */
   onExport() {
    this.loaderService.invokeLoaderComponent(true);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Utilization');
  
    // Generate title with current date
    const title = 'Utilization_List_' + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
  
    // Collect headers from column definitions
    let headers: string[] = this.columnDefs.map((col: { headerName: string }) => col.headerName).filter(Boolean);
    worksheet.addRow(headers);
  
    // Style the header row (bold + center alignment)
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    });
  
    // Identify the index for "Capacity Utilization" column
    const capacityUtilizationIndex = this.columnDefs.findIndex((col: any) => col.field === 'capacityUtilization');
  
    // Identify dynamic day-PTO pairs (from excelData, not columnDefs)
    const dayPtoMap: { [dayField: string]: string } = {};
    if (this.excelData.length > 0) {
      Object.keys(this.excelData[0]).forEach((key) => {
        if (key.startsWith('day')) {
          const dayNumber = key.replace('day', '');
          const ptoField = `pto${dayNumber}`;
          if (ptoField in this.excelData[0]) {
            dayPtoMap[key] = ptoField; // Map day1 -> pto1, day2 -> pto2, etc.
          }
        }
      });
    }
  
    // Prepare and add data rows
    this.excelData.forEach((rowData: any) => {
      // Map data according to column definitions (maintain correct order)
      const rowValues = this.columnDefs.map((col: any) => {
        if (col.field === 'capacityUtilization') {
          return rowData.capacityUtilizationDetails?.capacityUtilization ?? rowData[col.field];
        }
        return rowData[col.field];
      });
  
      const excelRow = worksheet.addRow(rowValues);
  
      // Apply styles for 'Capacity Utilization' column
      if (capacityUtilizationIndex !== -1) {
        const cell = excelRow.getCell(capacityUtilizationIndex + 1);
        const details = rowData.capacityUtilizationDetails;
  
        if (details) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: (details.backgroundColor || '#FFFFFF').replace('#', '') },
          };
          cell.font = {
            color: { argb: (details.fontColor || '#000000').replace('#', '') },
          };
          cell.border = {
            top: { style: 'thin', color: { argb: (details.borderColor || '#000000').replace('#', '') } },
            left: { style: 'thin', color: { argb: (details.borderColor || '#000000').replace('#', '') } },
            bottom: { style: 'thin', color: { argb: (details.borderColor || '#000000').replace('#', '') } },
            right: { style: 'thin', color: { argb: (details.borderColor || '#000000').replace('#', '') } },
          };
        }
      }
  
      // Apply red background for 'dayN' columns where 'ptoN' is 'y'
      Object.entries(dayPtoMap).forEach(([dayField, ptoField]) => {
        const dayIndex = this.columnDefs.findIndex((c: any) => c.field === dayField) + 1; // Excel is 1-based
        if (dayIndex > 0 && rowData[ptoField] === 'y') {
          const dayCell = excelRow.getCell(dayIndex);
          dayCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FF0000' }, // Red background for PTO days
          };
        }
      });
    });
  
    // Adjust column widths for better readability
    worksheet.columns.forEach((column) => {
      column.width = 20;
    });
  
    // Export workbook as an Excel file
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, title + '.xlsx');
    });
  
    this.loaderService.invokeLoaderComponent(false);
  }
  
  
  
  
}


