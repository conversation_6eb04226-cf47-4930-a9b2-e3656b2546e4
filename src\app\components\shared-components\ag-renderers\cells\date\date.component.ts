import { Component } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import moment from 'moment';

@Component({
    selector: 'app-date',
    templateUrl: './date.component.html',
    styleUrls: ['./date.component.scss'],
    standalone: false
})
export class DateComponent implements AgRendererComponent {

  enableCopy:boolean = false;
  currCellWidth:any;
  cellValue:any;
  params: any;
  agInit(params: ICellRendererParams): void {
    this.params = params;
    let colDef:any = params.colDef
    this.enableCopy = (colDef?.enableCopy)? true : false;
    this.cellValue = this.getValueToDisplay(params)
  }
  refresh(params: ICellRendererParams):boolean {
    this.cellValue = this.getValueToDisplay(params);
    return true
  }
  /**
   * Get Value To Display
   * Format into the MM-DD-YYYY format if the field have value
   * @param params
   * @returns
   */
  getValueToDisplay(params: ICellRendererParams) {
    return (params.value==null || params.value=='')?'':moment(params.value).format('MM-DD-YYYY')
  }
  /**
   * Set Cell Width
   * @param params
   */
  private setCellWidth(params: any): void { this.currCellWidth = params.column.actualWidth - 10; }
}
