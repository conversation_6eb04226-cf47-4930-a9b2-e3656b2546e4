<div class="row pt-2">
    <div class="search-btn-pmo justify-content-end">
        <div class="excel-btn px-2">
            <button type="button" class="ant-btn btn btn-success" title="Export"
                [disabled]="isPastDateLoader || isMissingDueDateLoader || isMissingEstimateLoader || isRiskLoader"
                (click)="onExportPMO()">
                <img [src]="assetURL +'/assets/images/Excel-Icon.png'">
            </button>
        </div>
    </div>
</div>
<div class="row mt-1">
    <nz-collapse [nzAccordion]="false" [nzExpandIconPosition]="'end'" class="border-0 bg-transparent">
        <nz-collapse-panel nzHeader="Past Due Tickets" class="bg-accordian-button-custom" [nzActive]="true">
            <nz-spin [nzSpinning]="isPastDateLoader">
                <app-past-due-date-grid [rowData]="pastDueDateData" [columnDefs]="columnDefs" [gridStyle]="gridStyle"
                    [defaultColDef]="defaultColDef" [components]="components"
                    [noRowsTemplate]="noRowsTemplate" [gridOptions]="gridOptions"></app-past-due-date-grid>
            </nz-spin>
        </nz-collapse-panel>
        <nz-collapse-panel nzHeader="Missing Due Dates" class="bg-accordian-button-custom" [nzActive]="true">
            <nz-spin [nzSpinning]="isMissingDueDateLoader">
                <app-missing-due-date-grid [rowData]="missingDueDateData" [columnDefs]="missingDueDateColumnDefs"
                    [gridStyle]="gridStyle" [defaultColDef]="defaultColDef" [components]="components"
                    [noRowsTemplate]="noRowsTemplate" [gridOptions]="gridOptions"></app-missing-due-date-grid>
            </nz-spin>
        </nz-collapse-panel>
        <nz-collapse-panel nzHeader="Missing Estimates" class="bg-accordian-button-custom" [nzActive]="true">
            <nz-spin [nzSpinning]="isMissingEstimateLoader">
                <app-missing-estimate-date-grid [rowData]="missingEstimateData" [columnDefs]="missingEstimateColumnDefs"
                    [gridStyle]="gridStyle" [defaultColDef]="defaultColDef" [components]="components"
                    [noRowsTemplate]="noRowsTemplate" [gridOptions]="gridOptions"></app-missing-estimate-date-grid>
            </nz-spin>
        </nz-collapse-panel>
        <nz-collapse-panel nzHeader="Risk Tickets" class="bg-accordian-button-custom" [nzActive]="true">
            <nz-spin [nzSpinning]="isRiskLoader">
                <app-at-risk-grid [rowData]="riskData" [columnDefs]="riskColumnDefs" [gridStyle]="gridStyle"
                    [defaultColDef]="defaultColDef" [components]="components"
                    [noRowsTemplate]="noRowsTemplate" [gridOptions]="gridOptions"></app-at-risk-grid>
            </nz-spin>
        </nz-collapse-panel>
    </nz-collapse>
</div>
<div class="sticky-buttons-group">
    <button nz-button nzType="primary" *ngIf="!isDivVisible" (click)="toggleDiv()" class="sticky-btn"
        matTooltip="Status Key" matTooltipPosition="after"><span><i class="fa fa-info-circle"></i></span></button>
    <div class="toggle-div" *ngIf="isDivVisible">
        <button (click)="toggleDiv()" class="close"><span><i class="fa fa-times"></i></span></button>
        <h6 class="status-indicator-head">Legends</h6>
        <ul *ngIf="getLegendsLength()">
            <li *ngFor="let status of getLegends()"
                [ngStyle]="{'background-color': status.backgroundColor,'color':status.fontColor,'border': '2px solid', 'borderColor': status.borderColor}">
                {{status.category}}</li>
        </ul>
    </div>
</div>