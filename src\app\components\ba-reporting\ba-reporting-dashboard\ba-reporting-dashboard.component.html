
<div class="container-fluid" id="agile-container">
  <div>
    <div class="row mb-1">
      <app-ba-reporting-filter  (emitFilters)="getFilterChange($event)" ></app-ba-reporting-filter>
    </div>
    <div class="row">
      @if (gridMode == 'PMO_EOD_REPORTING') {
        <app-pmo-eodreporting [pmoEODData]="pmoEodData"></app-pmo-eodreporting>
      }
      @if (gridMode == 'EOD_REPORTING') {
        <app-eod-reporting [allApplicationBugs]="allApplicationBugs"
          [finalLoader]="finalLoader"
          [activeChangeManagementRequests]="activeChangeManagementRequests"
          [isApplicationBugLoader]="isApplicationBugLoader"
          [salesforceCases]="salesforceCases"
          [salesForceSupportCases]="salesForceSupportCases"
          [oneButtonTotal]="oneButtonData"
          [oneDrawTotal]="oneDrawData"
          [baStatusUpdateData]="baStatusUpdateData"
          [currentlyOpenCases]="currentlyOpenCases"
          [currentlyOpenCasesColDef]="currentlyOpenColDefs"
          [closedCases]="closedCases"
          [closedColDef]="closedColDefs"
          [openedTodayCases]="openedTodayCases"
          [openedTodayColDef]="openedTodayColDefs"
          [oneButtonPriorityData]="oneButtonPriorityData"
          [oneDrawColDef]="oneDrawColDef"
        ></app-eod-reporting>
      }
    </div>
  </div>
</div>