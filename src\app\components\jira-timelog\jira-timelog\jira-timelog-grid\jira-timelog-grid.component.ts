import { Component, Input, OnInit } from '@angular/core';
import { SharedService } from 'src/app/service/shared.service';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { HourComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour/hour.component';
import { NormalComponent } from 'src/app/components/shared-components/ag-renderers/cells/normal/normal.component';
import { NormalHeaderComponent } from 'src/app/components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
interface TeamMember {
  memberName: string;
  hours: number;
}
 
interface Epic {
  epicKey: string;
  teamMember: TeamMember[];
}
 
interface Project {
  projectName: string;
  epic: Epic[];
  hoursTotal: number;
}
 
interface Team {
  teamName: string;
  project: Project[];
  hoursTotal: number;
}
 
interface RowData {
  isTeam: boolean;
  team: string | null;
  isProject: boolean;
  project: string | null;
  isEpic: boolean;
  epic: string | null;
  user: string | null;
  hours: number;
  isTotal: boolean;
  total: number;
  isTeamTotal: boolean;
}
@Component({
    selector: 'app-jira-timelog-grid',
    templateUrl: './jira-timelog-grid.component.html',
    styleUrls: ['./jira-timelog-grid.component.scss'],
    standalone: false
})
export class JiraTimelogGridComponent {
  

  constructor(private sharedService: SharedService
  ) { }
  gridStyle: string = dashboardConstants.gridStyle;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  columnDefs: any[] = dashboardConstants.jiraTimeLogColDef;
  // Component registry for ag-grid v30+
  components = {
    normalHeader: NormalHeaderComponent,
    normalRenderer: NormalComponent,
    hourRenderer: HourComponent,
  };

  gridApi: any;
  rowData:any[]=[];
  @Input() inputData: Team[] = [];
  @Input() timeLogFlag: boolean = false;
  emptyTemplate = `<span></span>`;
   gridOptions= {
    ...dashboardConstants.gridOptions, // Include theme: "legacy"
    groupIncludeFooter: true, // Show totals per group
    groupIncludeTotalFooter: true, // Optional: Show grand total
    suppressAggFuncInHeader: true,
    animateRows: true,
    defaultColDef: {
      resizable: true,
      sortable: true,
    }
  }
  ngOnChanges() {
    if(this.inputData) {
      this.rowData = this.sharedService.convertToFlatArray(this.inputData);
    }
  }
/**
 * Triggered when the grid is ready.
 * @param params - Grid API parameters
 */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    this.updateColumnSize(this.gridApi);
  }
  /**
   * Adjusts column sizes dynamically when the window resizes.
   * @param param 
   */
  updateColumnSize(param: any) { window.addEventListener('resize', () => { setTimeout(() => param.sizeColumnsToFit(), 500) }) }
}
