import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-at-risk-grid',
    templateUrl: './at-risk-grid.component.html',
    styleUrls: ['./at-risk-grid.component.scss'],
    standalone: false
})
export class AtRiskGridComponent implements OnInit {
  @Input() rowData: any[] = [];
  @Input() noRowsTemplate!: string;
  @Input() gridStyle!: string;
  @Input() defaultColDef: any;
  @Input() components: any;
  @Input() columnDefs: any;
  @Input() gridOptions: any;
  gridApi: any;

  constructor() { }

  ngOnInit(): void {
  }
  /**
   * Triggered when the grid is ready.
   * @param params - Grid API parameters
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
  }
}
