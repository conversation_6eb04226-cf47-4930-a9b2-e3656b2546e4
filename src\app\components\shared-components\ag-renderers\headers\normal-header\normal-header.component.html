<div class="overflow-hidden" [ngClass]="{'text-center': params.displayName ==='#Hrs' || params.displayName ==='Remaining' || params.displayName ==='#Hrs' || params.displayName ==='% Hrs Complete'}">
  <button class="sorting-button text-center" (click)="sort(params.column.userProvidedColDef.sortOrder)">
    {{ params.displayName}}
    @if (!params.column.userProvidedColDef || !params.column.userProvidedColDef.sortOrder || params.column.userProvidedColDef.sortOrder == '') {
      <i class="hidden-icon fa fa-arrow-up" style="-webkit-text-stroke: 0.5px white;" aria-hidden="true"></i>
    }
    @if (params.column.userProvidedColDef.sortOrder == 'asc') {
      <i class="fa fa-arrow-up" style="-webkit-text-stroke: 0.5px white;" aria-hidden="true"></i>
    }
    @if (params.column.userProvidedColDef.sortOrder == 'desc') {
      <i class="fa fa-arrow-down" style="-webkit-text-stroke: 0.5px white;" aria-hidden="true"></i>
    }
  </button>
  @if (params.enableMenu) {
    <span #menuButton class="customHeaderMenuButton pt-2"  (click)="onMenuClicked($event)" (touchstart)="onMenuClicked($event)">
      @if (!params.column.userProvidedColDef?.headerComponentParams?.filterEnable) {
        <i class="fa fa-bars" aria-hidden="true"></i>
      }
      @if (params.column.userProvidedColDef?.headerComponentParams?.filterEnable) {
        <i class="fa fa-filter" aria-hidden="true"></i>
      }
    </span>
  }
</div>
