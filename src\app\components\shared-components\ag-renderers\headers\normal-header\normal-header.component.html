<div class="overflow-hidden" [ngClass]="{'text-center': params.displayName ==='#Hrs' || params.displayName ==='Remaining' || params.displayName ==='#Hrs' || params.displayName ==='% Hrs Complete'}">
  <button class="sorting-button text-center" (click)="sort(params.column.userProvidedColDef.sortOrder)">
    {{ params.displayName}}
    <i *ngIf="!params.column.userProvidedColDef || !params.column.userProvidedColDef.sortOrder || params.column.userProvidedColDef.sortOrder == ''" class="hidden-icon fa fa-arrow-up" style="-webkit-text-stroke: 0.5px white;" aria-hidden="true"></i>
    <i *ngIf="params.column.userProvidedColDef.sortOrder == 'asc'" class="fa fa-arrow-up" style="-webkit-text-stroke: 0.5px white;" aria-hidden="true"></i>
    <i *ngIf="params.column.userProvidedColDef.sortOrder == 'desc'" class="fa fa-arrow-down" style="-webkit-text-stroke: 0.5px white;" aria-hidden="true"></i>
  </button>
  <span *ngIf="params.enableMenu" #menuButton class="customHeaderMenuButton pt-2"  (click)="onMenuClicked($event)" (touchstart)="onMenuClicked($event)">
    <i class="fa fa-bars" *ngIf="!params.column.userProvidedColDef?.headerComponentParams?.filterEnable" aria-hidden="true"></i>
    <i class="fa fa-filter" *ngIf="params.column.userProvidedColDef?.headerComponentParams?.filterEnable" aria-hidden="true"></i>
  </span>
</div>
