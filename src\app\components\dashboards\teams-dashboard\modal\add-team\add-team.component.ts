import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';


@Component({
    selector: 'app-add-team',
    templateUrl: './add-team.component.html',
    styleUrls: ['./add-team.component.scss'],
    standalone: false
})
export class AddTeamComponent implements OnInit {
  @ViewChild('modalFooterTemplate', { static: true }) modalFooterTemplate!: TemplateRef<any>;
  teamForm!: FormGroup;

  constructor(
    private formBuilder: FormBuilder,
    private onePmService: OnepmServiceService,
    private notification: NzNotificationService,
    private modalRef: NzModalRef<AddTeamComponent>,
    private loaderService: LoaderService,
  ) { }
  
  ngOnInit(): void {
    this.createTeamForm();
  }
  /**
   * Function is used to initialize team form 
   */
  createTeamForm(): FormGroup {
    return this.teamForm = this.formBuilder.group({
      teamName: ['',[Validators.required,Validators.pattern(/^(?!\s*$).+/)]],
      teamDescription: ['',[Validators.required,Validators.pattern(/^(?!\s*$).+/)]],
    });
  }
  /**
   * Function for submit 
   */
  onSubmit():void {
    if (this.teamForm.valid) {
      this.loaderService.invokeLoaderComponent(true);
      let userId = localStorage.getItem('userEmail');
      let body = {
        teamName: this.teamForm.value.teamName.trim(),
        teamDescription: this.teamForm.value.teamDescription.trim(),
        userId: userId
      };
      this.onePmService.addTeamsData(body).subscribe(
        (res) => {
          this.loaderService.invokeLoaderComponent(false);
          if(res.succeeded){
          this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'success' });
          this.resetForm();
          this.modalRef.close(true); // Close the modal on success
          }  else {
            this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
          }
        },
        (error) => {
          this.loaderService.invokeLoaderComponent(false);
          this.modalRef.close(false); 
          this.notification.blank('', error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      );
    } else {
      this.notification.blank('', 'Fill the Required Fields', { nzPlacement: 'bottomLeft', nzClass: 'error' });

    }
  }
  /**
   * Function for reset 
   */
  resetForm(): void {
    this.teamForm.reset();
  }
}