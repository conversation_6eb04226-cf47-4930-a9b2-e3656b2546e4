import { ComponentFixture, TestBed } from '@angular/core/testing';

import { KpiDeploymentSuccessComponent } from './kpi-deployment-success.component';

describe('KpiDeploymentSuccessComponent', () => {
  let component: KpiDeploymentSuccessComponent;
  let fixture: ComponentFixture<KpiDeploymentSuccessComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ KpiDeploymentSuccessComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(KpiDeploymentSuccessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
