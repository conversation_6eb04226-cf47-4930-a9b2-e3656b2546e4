::ng-deep {
    .bg-accordian-button-custom {
        .ant-collapse-header {
            color: white !important;
        }

        background-color: rgb(84, 137, 187) !important;

        .ant-collapse-content-box {
            padding: 2px !important;
        }
    }
}

::ng-deep .ant-collapse-content-box {
    padding: 8px !important;
    /* Adjust padding */
    min-height: 20px !important;
    /* Set desired minimum height */
}

::ng-deep .ant-collapse-header {
    padding: 8px !important;
    /* Adjust padding for header */
    min-height: 32px !important;
    /* Adjust header height */
}

button span i {
    font-size: 16px !important;
    margin-left: -5px;
}

li {
    list-style-type: none;
    text-align: center;
    list-style-type: none;
    text-align: center;
    padding: 2px;
    font-weight: 600;
    font-size: 10px;
    margin: 6px;
}

.status-indicator-head {
    text-align: center;
    font-size: 12px;
    margin: 10px;
}

.close {
    border: none;
    background: none;
    float: right;
}

.close:hover {
    color: #40a9ff;
}