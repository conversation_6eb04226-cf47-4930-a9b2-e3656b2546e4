body,input,.inner_container,.mat-table,button,select,optgroup,textarea,mat-checkbox,i {
    font-family: 'Poppins', sans-serif ;
}

body {
    height: auto;
    width: 100%;
}

h1,h2,h3,h4,h5,h6 {
    color: rgb(85 85 85);
    margin-bottom: 0.75rem;
    font-weight: bold;
}

.form-control {
    font-size: 12px !important;
    padding: 0.5rem 0.75rem !important;
}

.form-panel .form-field {
    display: inline-block !important;
    width: 200px !important;
    margin: 5px !important;
    position: relative;
    vertical-align: top;
}

.form-panel .form-field.double-width {
    width: 410px !important;
}


.form-select {
    font-size: 12px !important;
    padding: 0.5rem 0.75rem !important;
    padding-right: 30px !important;
}

.form-select option {
    padding: 1rem !important;
}

.no-background {
    background-color: transparent !important;
}

.no-border-color {
    border-right-color: transparent !important;
}

.no-padding-left {
    padding-left: 0 !important;
}

hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    color: #b5b5b5;
}

.form-control,select,input {
    margin-left: 2px !important;
}

label {
    display: inline-block;
    color: rgba(0, 0, 0, .45);
    font-weight: lighter;
    font-size: 12px !important;
}

.details-header-text {
    color: #538ABC !important;
    /* font-weight: 600 !important; */
    font-size: 14px !important;
    display: flex;
    margin-bottom: 10px;
}

/*******MAIN**********/
main {
    overflow-y: auto;
    /* height: calc(100vh - 100px); */
}

.footer {
    position: fixed;
    bottom: 0;
    box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
    background: white;
    padding: 1rem;
    border-radius: 7px;
    right: 2rem;
    left: 2rem;
    margin-right: auto;
    margin-left: auto;
    /* width: 100%; */
    text-align: right;
}

/*** BUTTONS *********/

.dashed-button {
    border: 1px dashed grey !important;
    padding: 0.25rem 1rem !important;
    color: black !important;
    border-radius: 7px !important;
    min-width: 100px !important;
}

.active-button {
    border: 1px dashed grey !important;
    padding: 0.25rem 1rem !important;
    color: rgb(255, 255, 255) !important;
    border-radius: 7px !important;
    min-width: 100px !important;
    background-color: #538abc !important;
}

/********************/

/*******SIDE BAR********/
.side-menu-section {
    width: auto;
    background-color: white;
    box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
    border-radius: 6px;


    li:first-of-type {
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    li:last-of-type {
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
    }
}

.side_menu_bar {
    padding: 0;
}

.side-bar-link-label {
    display: none;
    font-size: 7px;
    color: #555555ad;
    text-wrap: nowrap;
}

.side-bar-link-icon {
    font-size: 1rem;
    color: #555555;
}

.side_menu_bar li {
    list-style: none;
    margin-bottom: 1px;
    padding: 0.5rem;
    cursor: pointer;
}

.side_menu_bar li:hover {
    background-color: #538abc !important;

    i {
        color: white !important;
    }

    .side_menu_bar li a.active .side-bar-link-label,
    .side_menu_bar li a.active .side-bar-link-icon {
        color: white !important;
    }
}

.side_menu_bar li:hover .side-bar-link-label {
    display: block !important;
    color: white !important;
}

.side_menu_bar li:hover .side-bar-link-icon {
    color: white !important;
}

.side_menu_bar li * {
    text-align: center;
    display: block;
    cursor: pointer;
}

.side_menu_bar li a {
    color: black;
    text-decoration: none;
}

.side_menu_bar li a.active .side-bar-link-label,
.side_menu_bar li a.active .side-bar-link-icon {
    color: #538abc;
}

.side_menu_bar li a.active:hover .side-bar-link-label,
.side_menu_bar li a.active:hover .side-bar-link-icon {
    color: white !important;
}


/****************************


/******CARD***********/
.ant-modal-header {
    background-color: #538abc !important;
    padding: 6px 15px !important;
}
.ant-modal-close{
    margin-top: -15px !important;
}

.ant-modal-title {
    color: white !important;
}

.ant-modal-body {
    overflow: scroll;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 70vh;
    padding-bottom: 5px;
}

.ant-card {
    box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
    margin: 2px;
    border-radius: 6px !important;

    .custom-card-footer {
        width: 100%;
        border-top: 1px solid #f5f6fa;
        position: absolute;
        bottom: 0;
        padding: 1rem;
        left: 0;
        right: 0;
    }
}

.ant-card-bordered {
    border: 0 !important;
}

.ant-card-body {
    padding: 7px !important;
}

.ant-card-head {
    height: 45px !important;
    padding: 0 20px !important;
    background-color: #538abc !important;
    color: white !important;
    border-radius: 6px 6px 0 0 !important;
}

.ant-card-head-wrapper {
    display: inline !important;
}

.ant-card-head-title {
    padding: 10px 0 !important;
    max-width: 90% !important;
}

.ant-card-extra {
    padding: 8px 0;

    label {
        color: #dee2e6 !important;
    }
}


.ant-menu-vertical>.ant-menu-submenu>.ant-menu-submenu-title {
    padding-left: 0 !important;
}

.ant-btn {
    font-size: 12px !important;
}

.ant-list-item-meta-description {
    font-size: 12px !important;
}

.ant-list-vertical .ant-list-item-meta-title {
    font-size: 14px !important;
    color: #434343;
}


.ant-badge-status-dot {
    width: 1rem !important;
    height: 1rem !important;
}

a {
    line-height: 1.5715 !important;
}

/* LIST ITEMS */
.ant-list-items {
    margin: 0;
    padding: 12px;
    list-style: none;
    background: #b0c5d80d;
    border-radius: 7px;
}

.ant-list-item {
    display: block !important;
    padding: 0.7rem 1rem;
    background: #6e8fb217;
    cursor: pointer;
    color: white;
    position: relative;
    border-radius: 6px;


    &:hover {
        background-color: #538abc !important;

        .ant-list-item-meta-title {
            display: block !important;
            color: white !important;
        }

        .ant-list-item-meta-description {
            color: white !important;
        }
    }
}

.ant-list-action-item-list {
    border-radius: 4px;
    color: white;
    display: block;
    width: 100%;
    margin-top: 1rem;
}

.ant-statistic-title {
    font-size: 12px !important;
}

.ant-statistic-content {
    font-size: 14px !important;
    margin-bottom: 1rem !important;
}

.ant-statistic {
    display: block !important;
}

.ant-tag {
    margin: 3px;
    display: inline-block;
    padding: 0.2rem 0.75rem;
}

.ant-descriptions-item-label {
    font-weight: bold !important;
}


/* .ant-radio-button-wrapper{ border-style: dashed !important; } */
/*********************/
.error-message {
    width: 34%;
    font-size: 14px;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-right: 10px;
    padding-left: 10px;
    margin-top: 3px;
    margin-left: auto;
    margin-right: 10px;
    color: red;
    text-align: right;
}

.invalid-feedbacks {
    width: 100%;
    margin-top: 0.25rem;
    font-size: 12px;
    margin-left: 8px;
    color: #dc3545;
}

/* #page-content { margin-bottom: 50px; } */
app-normal-header {
    width: 100%;
}

label.star {
    padding: 0 2px !important;
}

label.star:before {
    font-family: 'Font Awesome 5 Free' !important;
}

input.star:checked~label.star:before {
    font-weight: 600;
}

.mat-primary .mat-option.mat-selected {
    color: white !important;
}

.mat-icon-button.mat-button-disabled.mat-button-disabled {
    color: #d2cbcbde
}

.labelColor {
    display: inline-block;
    color: rgba(0, 0, 0, .45);
    font-weight: lighter;
    font-size: 14px !important;

    i {
        padding-right: 3px;
    }
}

.alert-list {
    .alert {
        .ng-star-inserted {
            .fa {
                line-height: 1 !important;
            }
        }

        .close {
            margin-top: -8px;

            .cross {
                font-size: 17px !important;
            }
        }
    }
}

.button-span-style {
    display: grid;
}

.fw-600 {
    font-weight: 600;
}

.no-filtered-data {
    h5 {
        font-size: 12px;
    }
}

.ant-drawer-header {
    background-color: #E2E8F9 !important;
    padding: 10px 10px !important;
    border-radius: 2px 2px 0 0 !important;
    ;
}

.ant-drawer-title {
    font-weight: 600 !important;
    line-height: 22px !important;
}

.ant-drawer-close {
    color: white !important;
    background-color: #538ABC !important;
    font-size: 20px;
    padding: 9px !important;
    margin-right: 0px !important;
}

.ant-drawer-body {
    line-height: 2;
}

.ag-simple-filter-body-wrapper {
    padding-bottom: 12px !important;
}

.overflow-hidden {
    padding-bottom: 6px;
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
    height: auto !important;
}

input[readonly] {
    background-color: #e9ecef !important;
}

.battery-adder-style {
    padding: 6px 15px 20px 15px;
    font-size: 12px !important;
}

.font-12 {
    font-size: 12px;
}

/* Quote version styles */
.inlineDisplay {
    display: flex;
}

.pr-0 {
    padding-right: 0px !important;
}

.mt-11 {
    margin-top: 11px;
}

.f-600 {
    font-weight: 600;
}

.detail-cell-row {
    padding: 6px 18px;
}

.input-group-addon {
    text-align: center;
    width: 32%;
    font-size: 12px;
    height: 37px;
    background-color: #eee;
    display: flex;
    border: 1px solid #ccc;
    align-content: flex-end;
    flex-direction: column;
    justify-content: space-around;
    flex-wrap: nowrap;
    border-radius: 6px 0px 6px 6px;
}

input {
    display: table-cell;
    border: 1px solid #ccc;
}

.input-group {
    padding: 4px 0px 4px 4px;
    border-collapse: separate;
}

.input-class {
    margin: 0px !important;
    height: 37px;
    border-radius: 0px 5px 5px 0px !important;
}

.p-center {
    text-align: center;
}

/** Copy to clipboard styles */
.copy-to-clip-board-icon {
    display: none;
    padding-left: 2px;
}

app-normal,app-opportunity-id-admin,app-date {
    width: 100%;
}

.copy-to-clip-board a,
.copy-to-clip-board p {
    width: 95%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.copy-to-clip-board:hover .copy-to-clip-board-icon {
    display: inline-block;
    cursor: pointer;
}

.copy-to-clip-board .fa-check-circle {
    color: green;
}

.copy-to-clip-board .fa-clone {
    color: #538ABC;
}

.ant-card-body .form-panel .form-field {
    position: relative !important;
}

.payment {
    display: flex;
    align-content: flex-start;
    align-items: center;
}

.total-display {
    margin-top: 11px;
    text-align: right;
}

.ml-4 {
    margin-left: 4px;
}

.fileupload-icon-style {
    background: #ffff url("../images/upload-icon.png") no-repeat right 7px bottom 9px;
    background-size: 15px 15px;
}

.fileupload-icon-style-1 {
    background: white url("../images/upload-icon.png") no-repeat right 30px bottom 9px;
    background-size: 15px 15px;
}

.toggle-btn-commission {
    font-size: 13px;
    margin-top: 5px;
    color: white;
}

.ant-notification-notice-content {
    margin-right: 5px;
}

[hidden] {
    display: none !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked {
    background-color: #e4e3e3 !important;
}
:host ::ng-deep .fas {
  font: normal !important; /* New properties */
}