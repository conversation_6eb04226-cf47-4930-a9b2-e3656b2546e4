.sorting-button{
  background: none;
  border: none;
  box-shadow: none;
  font-weight: 500;
  margin-top: 10px;
  .hidden-icon{ display: none; }
}
.sorting-button:hover{
  .hidden-icon{  display: inline; }
}
.forign-key-button{
  background: transparent;
  border: none;
  box-shadow: none;
}
.forign-key-button::after,.filter-button::after{ display: none; }
.dropdown-menu {
  padding: 0;
  .dropdown-item { padding: 0.375rem 2.25rem 0.375rem 0.75rem; }
}
.mdm-table-ico-1{
  img{
    padding-top: 5px;
    padding-right: 2px;
  }
}
.filter-button{
  color: transparent;
  background-color: transparent;
  border: none;
}
.filter-button:hover{ color: black; }
.customHeaderMenuButton {
  float: right;
  i{ cursor: pointer; }
  i.fa-bars{ color: gainsboro; }
  i.fa-bars:hover,i.fa-bars:active{ color: grey; }
}
.sorting-button{
  i{ line-height: 1 !important; }
}
