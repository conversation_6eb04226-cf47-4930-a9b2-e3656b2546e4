import { Component, OnInit } from '@angular/core';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';
import { Member, TransformedMember } from 'src/app/utils/apps.interface';

@Component({
    selector: 'app-ultilization-dashboard',
    templateUrl: './ultilization-dashboard.component.html',
    styleUrls: ['./ultilization-dashboard.component.scss'],
    standalone: false
})
/*
  Utilization Dashboard
*/
export class UltilizationDashboardComponent implements OnInit {
  colDefs:any[]=[];
  ultilizationColdef = dashboardConstants.ultilizationColdef.map(col => {
    const newCol = Object.assign({}, col);
    return newCol;
  });
  ultilizationSummaryColdef = dashboardConstants.ultilizationSummaryColdef;
  gridMode:string = '';
  gridData: any[] = [];
  rowData: any[] = [];
  chartData: any;

  constructor(private onePmService: OnepmServiceService, private loaderService: LoaderService) { }

  ngOnInit(): void {
    
  }
  
  /**
   * Handles filter changes and updates the grid data accordingly.
   * @param event Contains filter details and whether to clear filters.
   */
  getFilterChange($event: any) {
    this.rowData = [];
    this.gridMode = $event.data.groupName;
    this.colDefs = this.generateColumnDefs($event.data.fromDate, $event.data.toDate);
    this.getUtilizationList($event.data);    
  }
/**
 * Function to generate column definitions based on the grid mode and date range.
 * @param startDate 
 * @param endDate 
 * @returns 
 */
  generateColumnDefs(startDate: string, endDate: string) {
    this.ultilizationColdef = dashboardConstants.ultilizationColdef.map(col => {
      return { ...col };
    });
  
    if (this.gridMode === 'Summary') {
      return [...this.ultilizationSummaryColdef];
    }
  
    const dateColumns = [];
    let currentDate = new Date(startDate);
    const end = new Date(endDate);
    let dayIndex = 1;
  
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 6 = Saturday
  
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Skip weekends
        const formattedDate = currentDate.toLocaleDateString('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: 'numeric',
        });
  
        // Create a block-scoped copy of dayIndex
        const currentDayIndex = dayIndex;
  
        dateColumns.push({
          field: `day${currentDayIndex}`,
          headerName: formattedDate,
          minWidth: 120,
          flex: 1,
          cellClass: 'cell-center',
          headerClass: 'wrap-header',
          cellStyle: (params: any) => {            
            const ptoField = `pto${currentDayIndex}`;
            return params.data && params.data[ptoField] === 'y'
              ? { backgroundColor: 'orangered', color: '#FFFFFF' }
              : {};
          },
        });
  
        dayIndex++;
      }
  
      currentDate.setDate(currentDate.getDate() + 1);
    }
  
    return [...this.ultilizationColdef, ...dateColumns];
  }
  
  
/**
   * Fetches Utlization data based on the provided filter criteria.
   * @param data The filter criteria used to fetch Utilization data.
   */
  getUtilizationList(data: any) {
    this.loaderService.invokeLoaderComponent(true);

    this.onePmService.getUtilizationInfo(data).subscribe((res: any) => {
      this.loaderService.invokeLoaderComponent(false);      
      if(this.gridMode !== 'Summary'){
        this.rowData = this.transformRowData(res.data.tableViewInfo);
        this.chartData = res.data.chartViewInfo;
      }else{
        this.rowData = res.data.tableViewInfo;
        this.chartData = res.data.chartViewInfo;
      }      
    })

  }
/**
 * Function to transform row data for the grid to day1 to dayn from dailyWorklogs array
 * @param rowData 
 * @returns 
 */
  transformRowData(rowData: Member[]): TransformedMember[] {
    return rowData.map((member) => {
      // Create a shallow copy without the dailyWorklogs property
      const { dailyWorklogs, ...rest } = member;
      const transformed: TransformedMember = { ...rest };
  
      dailyWorklogs.forEach((worklog, index) => {
        transformed[`day${index + 1}`] = worklog.hoursWorked;
        transformed[`pto${index + 1}`] = worklog.tookPTO;
      });
  
      return transformed;
    });
  }

}
