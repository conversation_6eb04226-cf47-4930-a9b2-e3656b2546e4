export const onepmApi = {
  projectData: [
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address <ul> <li>Test 1</li> <li>Test 2</li></ul>',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: '3', bg: '', br: '' },
      remaining: { value: '2', bg: '', br: '' },
      hrsComplete: { value: '1', bg: '', br: '' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: '3',
      remaining: '2',
      hrsComplete: '1',
    },
  ],
  dalyDashboardData: [
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
    {
      project: 'oneBUTTON',
      epic: 'OBA-1236',
      desc: 'As a sales representative, I want to sign a combined pack even when multiple users have the same email address, as long as they have different signing roles, so that all required signatures can be collected without conflict.',
      todaysDes: 'Completed V1',
      startDate: '01/01/2022',
      dueDate: '01/01/2022',
      status: 'In Progress',
      hours: { value: 3, bg: 'yellow', br: 'blue' },
      remaining: { value: 3, bg: 'yellow', br: 'blue' },
      hrsComplete: { value: 3, bg: 'yellow', br: 'blue' },
    },
  ],
  masterData: {
    project: [
      { id: 1, name: 'oneBUTTON' },
      { id: 2, name: 'onePERM' },
      { id: 3, name: 'onePAY' },
      { id: 4, name: 'oneROOF' },
      { id: 5, name: 'oneBUDGET' },
      { id: 6, name: 'oneTEAM' },
    ],
    teams: [
      { id: 1, name: 'BA' },
      { id: 2, name: 'Data' },
      { id: 3, name: 'App Dev' },
      { id: 4, name: 'PMO' },
      { id: 5, name: 'RPA' },
      { id: 6, name: 'Infrastructure' },
    ],
    members: [
      { id: 1, name: 'Anu' },
      { id: 2, name: 'Akhil' },
      { id: 3, name: 'Jibin' },
      { id: 4, name: 'Praveen' },
      { id: 5, name: 'Phani' },
      { id: 6, name: 'Remya' },
    ],
  },
  pmoPastData: [
    {
      project: 'Robotic Process Automation',
      key: 'RPA-1720',
      summary: 'RPA Score card for automation opportunities',
      assignee: 'Avi Goyal',
      developmentStage: 'In Analysis - Active',
      dueDate: '01-28-2025',
    },
    {
      project: 'Robotic Process Automation',
      key: 'RPA-1598',
      summary:
        'Review finished documentation and ensure information is not missed',
      assignee: 'Jake Merrick',
      developmentStage: 'UAT Testing - To Do',
      dueDate: '01-28-2025',
    },
    {
      project: 'PowerBI',
      key: 'RBD-2342',
      summary: 'Requirements Documentation - Contract Review',
      assignee: 'manu.goyal',
      developmentStage: 'Requirements Gathering - Active',
      dueDate: '01-28-2025',
    },
    {
      project: 'oneDRAW',
      key: 'ODR-1224',
      summary: 'Create DI job for Salesperson License',
      assignee: 'Rohan Nayak',
      developmentStage: 'In Development - To Do',
      dueDate: '01-28-2025',
    },
    {
      project: 'SalesForce Maintenance',
      key: 'SMS-7100',
      summary: 'Sun Path Simulation Not Working Correctly on iPad',
      assignee: 'Sandra John C',
      developmentStage: 'In Development - To Do',
      dueDate: '01-28-2025',
    },
    {
      project: 'Robotic Process Automation',
      key: 'RPA-1706',
      summary:
        'P-03: [Change Management] - Change Management Request for Billie Klecko : Phase 1 - Enhancements to Calculate Pass Yield and Cycle Times/Building & Implementing Constraints',
      assignee: 'Sreedhar Lakkavaram',
      developmentStage: 'Requirements Gathering - Active',
      dueDate: '01-28-2025',
    },
  ],
  pmoRiskData: [
    {
      project: 'oneCAL (OCAL)',
      key: 'OCAL-34',
      summary: 'Python Scripts and Function Apps',
      assignee: 'Yasar',
      developmentStage: 'In Analysis - Active',
      dueDate: '01-28-2025',
      estimate: 32,
      spent: '34.48',
      effort: '-2.48',
      time: '27',
    },
    {
      project: 'oneCAL (OCAL)',
      key: 'OCAL-40',
      summary: 'Learn Power Apps - John',
      assignee: 'John Veneziano',
      developmentStage: 'In Analysis - Active',
      dueDate: '01-28-2025',
      estimate: 32,
      spent: '6',
      effort: '26',
      time: '11',
    },
    {
      project: 'SalesForce Maintenance (SMS)',
      key: 'SMS-7141',
      summary: 'P-06: [Change Management] - Split NJ North Sales Office',
      assignee: 'Duaet Joel Thankachan',
      developmentStage: 'In Development - Active',
      dueDate: '01-28-2025',
      estimate: 8,
      spent: '9.15',
      effort: '-1.15',
      time: '11',
    },
    {
      project: 'PowerBI (RBD)',
      key: 'RBD-2362',
      summary: 'Add Percent Difference between actual and budget',
      assignee: 'John Veneziano',
      developmentStage: 'In Development - Active',
      dueDate: '01-28-2025',
      estimate: 4,
      spent: '7',
      effort: '-3',
      time: '27',
    },
    {
      project: 'oneBUTTON (OBA)',
      key: 'OBA-3289',
      summary:
        'Add Adobe signature tag to Direct Layout created by Genesis and oneDRAW',
      assignee: 'Christopher Stuckey',
      developmentStage: 'In Development - Active',
      dueDate: '01-28-2025',
      estimate: 20,
      spent: '0',
      effort: '20',
      time: '27',
    },
  ],
  hoursAndTicket: [
    {
      project: 'BT Dashboard (BT Dashboard)',
      ticket: 8,
      totalTime: 101.03,
      percentage: '2.22%',
    },
    {
      project: 'BT Support (BT Support)',
      ticket: 20,
      totalTime: 189.17,
      percentage: '4.15%',
    },
    {
      project: 'Community Partners (Community Partners)',
      ticket: 5,
      totalTime: 4.35,
      percentage: '0.10%',
    },
    {
      project: 'Content Management System (Content Management System)',
      ticket: 6,
      totalTime: 12.75,
      percentage: '0.28%',
    },
    {
      project: 'Excel Services (Excel Services)',
      ticket: 1,
      totalTime: 0.5,
      percentage: '0.01%',
    },
    {
      project: 'Infrastructure Initiatives (Infrastructure Initiatives)',
      ticket: 78,
      totalTime: 563.72,
      percentage: '12.38%',
    },
    {
      project: 'Innovation - 2024 (Innovation - 2024)',
      ticket: 1,
      totalTime: 8.0,
      percentage: '0.18%',
    },
    {
      project: 'LeadSiteInfo (LeadSiteInfo)',
      ticket: 10,
      totalTime: 50.83,
      percentage: '1.12%',
    },
    {
      project: 'Legacy Genesis (Legacy Genesis)',
      ticket: 6,
      totalTime: 197.2,
      percentage: '4.33%',
    },
    {
      project: 'Onboarding & Onboarding (Onboarding & Onboarding)',
      ticket: 1,
      totalTime: 18.0,
      percentage: '0.36%',
    },
    {
      project: 'oneBUDGET (oneBUDGET)',
      ticket: 3,
      totalTime: 38.17,
      percentage: '0.79%',
    },
    {
      project: 'oneBUILD (oneBUILD)',
      ticket: 15,
      totalTime: 74.2,
      percentage: '1.63%',
    },
    {
      project: 'oneBUTTON (oneBUTTON)',
      ticket: 91,
      totalTime: 597.48,
      percentage: '13.12%',
    },
    {
      project: 'oneCAL (oneCAL)',
      ticket: 13,
      totalTime: 111.88,
      percentage: '2.46%',
    },
    {
      project: 'oneDOC (oneDOC)',
      ticket: 24,
      totalTime: 101.85,
      percentage: '2.24%',
    },
    {
      project: 'oneDRAW (oneDRAW)',
      ticket: 33,
      totalTime: 353.03,
      percentage: '7.76%',
    },
    {
      project: 'oneLEAD (oneLEAD)',
      ticket: 21,
      totalTime: 135.48,
      percentage: '2.97%',
    },
    {
      project: 'onePAY (onePAY)',
      ticket: 48,
      totalTime: 268.58,
      percentage: '5.90%',
    },
    {
      project: 'onePRICE (onePRICE)',
      ticket: 2,
      totalTime: 7.08,
      percentage: '0.16%',
    },
    {
      project: 'oneROOF (oneROOF)',
      ticket: 23,
      totalTime: 110.4,
      percentage: '2.42%',
    },
    {
      project: 'oneTEAM (oneTEAM)',
      ticket: 5,
      totalTime: 18.65,
      percentage: '0.37%',
    },
    {
      project: 'Opportunity Contract (Opportunity Contract)',
      ticket: 1,
      totalTime: 61.25,
      percentage: '1.34%',
    },
    {
      project: 'PANDA (PANDA)',
      ticket: 1,
      totalTime: 0.05,
      percentage: '0.00%',
    },
    {
      project: 'PowerBI (PowerBI)',
      ticket: 31,
      totalTime: 316.52,
      percentage: '6.95%',
    },
    {
      project: 'Robotic Process Automation (Robotic Process Automation)',
      ticket: 31,
      totalTime: 393.32,
      percentage: '8.63%',
    },
    {
      project: 'Safety Cross (Safety Cross)',
      ticket: 1,
      totalTime: 2.92,
      percentage: '0.06%',
    },
    {
      project: 'SalesForce Maintenance (SalesForce Maintenance)',
      ticket: 63,
      totalTime: 528.4,
      percentage: '11.60%',
    },
    {
      project: 'Salesforce Optimization (Salesforce Optimization)',
      ticket: 5,
      totalTime: 102.63,
      percentage: '2.26%',
    },
    {
      project: 'Skedulo (Skedulo)',
      ticket: 4,
      totalTime: 46.53,
      percentage: '1.02%',
    },
    {
      project: 'Spotio (Spotio)',
      ticket: 3,
      totalTime: 8.25,
      percentage: '0.18%',
    },
    {
      project: 'SunSpark (SunSpark)',
      ticket: 6,
      totalTime: 46.55,
      percentage: '1.02%',
    },
    {
      project: 'Trinity Website (Trinity Website)',
      ticket: 13,
      totalTime: 92.4,
      percentage: '2.03%',
    },
    {
      project: 'Totals:',
      ticket: 571,
      totalTime: 4556.18,
      percentage: '100%',
    },
  ],
  kpiHoursAnalysisByTeam: [
    {
      project: 'BT Dashboard (BD)',
      plannedBA: 0.0,
      plannedAppDev: 30.73,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 0.0,
      unplannedAppDev: 0.0,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 30.73,
      totalUnplannedHours: 0.0,
      totalHours: 30.73,
    },
    {
      project: 'BT Support (SUP)',
      plannedBA: 0.0,
      plannedAppDev: 0.0,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 75.22,
      unplannedAppDev: 0.0,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 0.0,
      totalUnplannedHours: 75.22,
      totalHours: 75.22,
    },
    {
      project: 'LeadStefano (LD)',
      plannedBA: 0.0,
      plannedAppDev: 0.0,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 0.0,
      unplannedAppDev: 50.12,
      unplannedData: 0.72,
      unplannedRPA: 0.0,
      totalPlannedHours: 0.0,
      totalUnplannedHours: 50.83,
      totalHours: 50.83,
    },
    {
      project: 'Legacy Genesis (LG)',
      plannedBA: 0.0,
      plannedAppDev: 0.0,
      plannedData: 0.0,
      plannedRPA: 3.38,
      unplannedBA: 0.0,
      unplannedAppDev: 43.5,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 0.0,
      totalUnplannedHours: 43.5,
      totalHours: 43.5,
    },
    {
      project: 'oneBUILD (PA)',
      plannedBA: 4.58,
      plannedAppDev: 12.17,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 1.25,
      unplannedAppDev: 0.0,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 16.75,
      totalUnplannedHours: 1.25,
      totalHours: 18.0,
    },
    {
      project: 'oneBUTTON (OBA)',
      plannedBA: 0.0,
      plannedAppDev: 62.53,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 46.62,
      unplannedAppDev: 0.0,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 62.53,
      totalUnplannedHours: 46.45,
      totalHours: 134.98,
    },
    {
      project: 'oneCAL (LOCAL)',
      plannedBA: 0.0,
      plannedAppDev: 0.0,
      plannedData: 67.07,
      plannedRPA: 0.0,
      unplannedBA: 0.0,
      unplannedAppDev: 13.05,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 67.07,
      totalUnplannedHours: 13.05,
      totalHours: 80.12,
    },
    {
      project: 'oneDOC (OD)',
      plannedBA: 0.5,
      plannedAppDev: 0.0,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 2.67,
      unplannedAppDev: 0.0,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 0.5,
      totalUnplannedHours: 5.87,
      totalHours: 58.07,
    },
    {
      project: 'oneDRAW (ODR)',
      plannedBA: 0.0,
      plannedAppDev: 0.33,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 21.38,
      unplannedAppDev: 121.03,
      unplannedData: 0.58,
      unplannedRPA: 0.0,
      totalPlannedHours: 0.33,
      totalUnplannedHours: 143.0,
      totalHours: 143.33,
    },
    {
      project: 'oneLEAD (OL)',
      plannedBA: 0.0,
      plannedAppDev: 0.0,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 0.0,
      unplannedAppDev: 5.58,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 0.0,
      totalUnplannedHours: 5.58,
      totalHours: 5.58,
    },
    {
      project: 'onePAY (COM)',
      plannedBA: 0.0,
      plannedAppDev: 0.0,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 0.0,
      unplannedAppDev: 77.67,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 0.0,
      totalUnplannedHours: 77.67,
      totalHours: 77.67,
    },
    {
      project: 'oneROOF (RA)',
      plannedBA: 0.58,
      plannedAppDev: 84.5,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 85.06,
      unplannedAppDev: 6.05,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 85.08,
      totalUnplannedHours: 6.05,
      totalHours: 91.13,
    },
    {
      project: 'oneTEAM (PROJ)',
      plannedBA: 0.0,
      plannedAppDev: 1.0,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 1.0,
      unplannedAppDev: 0.0,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 1.0,
      totalUnplannedHours: 0.0,
      totalHours: 1.0,
    },
    {
      project: 'PowerBI (RBD)',
      plannedBA: 0.0,
      plannedAppDev: 1.0,
      plannedData: 60.85,
      plannedRPA: 0.0,
      unplannedBA: 0.0,
      unplannedAppDev: 29.67,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 61.85,
      totalUnplannedHours: 29.67,
      totalHours: 91.52,
    },
    {
      project: 'Total:',
      plannedBA: 470.6,
      plannedAppDev: 0.0,
      plannedData: 0.0,
      plannedRPA: 0.0,
      unplannedBA: 892.03,
      unplannedAppDev: 0.0,
      unplannedData: 0.0,
      unplannedRPA: 0.0,
      totalPlannedHours: 470.6,
      totalUnplannedHours: 892.03,
      totalHours: 1362.63,
    },
  ],
  kpiKeyFeatures: [
    { isMerged: true, mergedText: 'BT Dashboard (BD)' },
    {
      isMerged: false,
      project: 'BT Dashboard (BD)',
      ticketId: 'BD-58',
      description:
        '[onePM-UI] Color code implementation in Both Daily status and Project Level Report',
      loggedHours: '12.17 Hours',
    },
    {
      isMerged: false,
      project: 'BT Dashboard (BD)',
      ticketId: 'BD-57',
      description:
        '[onePM-API] Color code implementation in Both Daily status and isMerged: false, Project Level Report',
      loggedHours: '10.48 Hours',
    },
    {
      isMerged: false,
      project: 'BT Dashboard (BD)',
      ticketId: 'BD-59',
      description:
        '[onePAM-UI] isMerged: false, Project Level Report And Daily Status Report Excel Export',
      loggedHours: '8.08 Hours',
    },

    { isMerged: true, mergedText: 'BT Support (SUP)' },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-2512',
      description: 'Eddie Griffin - Misc Time',
      loggedHours: '18.08 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-4192',
      description: 'Christopher Stuckey - Misc Time',
      loggedHours: '9.30 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-2514',
      description: 'Ashane Robert - Misc Time',
      loggedHours: '8.75 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-19',
      description: '[Salesforce] Support Time',
      loggedHours: '7.83 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-16',
      description: '[Ring Central] Support Time',
      loggedHours: '5.33 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-2500',
      description: 'Jeff MacDonald - Misc Time',
      loggedHours: '4.25 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-27',
      description: '[oneBUTTON] Support Time',
      loggedHours: '4.25 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-18',
      description: '[Spotio] Support Time',
      loggedHours: '3.08 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-2507',
      description: 'Khalid Mansoor - Misc Time',
      loggedHours: '3.00 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-1862',
      description: '[oneLEAD] Support Time',
      loggedHours: '1.92 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-34',
      description: '[Box] Support Time',
      loggedHours: '1.83 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-2510',
      description: 'Tim Higgins - Misc Time',
      loggedHours: '1.75 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-28',
      description: '[onePAY] Support Time',
      loggedHours: '1.75 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-17',
      description: '[Skedulo] Support Time',
      loggedHours: '1.25 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-2505',
      description: 'Bryan Bigica - Misc Time',
      loggedHours: '1.25 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-29',
      description: '[oneROOF] Support Time',
      loggedHours: '0.92 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-4636',
      description: 'Site Capture Support',
      loggedHours: '0.25 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-30',
      description: '[oneDOC] Support Time',
      loggedHours: '0.25 Hours',
    },
    {
      isMerged: false,
      project: 'BT Support (SUP)',
      ticketId: 'SUP-23',
      description: '[Adobe Sign] Support Time',
      loggedHours: '0.17 Hours',
    },

    { isMerged: true, mergedText: 'Content Management System (CMS)' },
    {
      isMerged: false,
      project: 'Content Management System (CMS)',
      ticketId: 'CMS-70',
      description: 'CMS - Customer Consent Page',
      loggedHours: '1.00 Hours',
    },

    { isMerged: true, mergedText: 'Infrastructure Initiatives (IP)' },
    {
      isMerged: false,
      project: 'Infrastructure Initiatives (IP)',
      ticketId: 'IP-781',
      description: 'Vulnerability - Dependency Track BOM',
      loggedHours: '43.00 Hours',
    },
    {
      isMerged: false,
      project: 'Infrastructure Initiatives (IP)',
      ticketId: 'IP-786',
      description: 'Darktrace Alerts',
      loggedHours: '26.33 Hours',
    },
    {
      isMerged: false,
      project: 'Infrastructure Initiatives (IP)',
      ticketId: 'IP-784',
      description: 'January 2025 Administrative Work',
      loggedHours: '21.75 Hours',
    },
  ],
  kpiDeploymentSuccessUATData: [
    {
      project: ' Content Management System',
      sent: 'oneBUILD,PA,3,Content Management System, Customer NaN Portal,oneBUILD, oneBUTTON, oneDOC, oneDRAW, oneLEAD,one PAY, oneROOF,PowerBI, Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website',
      passed: '',
      failed:
        'oneBUTTON,OBA,31,Content Management System,Customer Portal, oneBUILD, oneBUTTON,oneDOC, oneDRAW,oneLEAD,onePAY,oneROOF,PowerBI,Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website (NaN)',
    },
    {
      project: ' Content Management System',
      sent: 'oneBUILD,PA,3,Content Management System, Customer NaN Portal,oneBUILD, oneBUTTON, oneDOC, oneDRAW, oneLEAD,one PAY, oneROOF,PowerBI, Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website',
      passed: '',
      failed:
        'oneBUTTON,OBA,31,Content Management System,Customer Portal, oneBUILD, oneBUTTON,oneDOC, oneDRAW,oneLEAD,onePAY,oneROOF,PowerBI,Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website (NaN)',
    },
  ],
  kpiDeploymentSuccessProdData: [
    {
      project: ' Content Management System',
      sent: 'oneBUILD,PA,3,Content Management System, Customer NaN Portal,oneBUILD, oneBUTTON, oneDOC, oneDRAW, oneLEAD,one PAY, oneROOF,PowerBI, Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website',
      passed: '',
      failed:
        'oneBUTTON,OBA,31,Content Management System,Customer Portal, oneBUILD, oneBUTTON,oneDOC, oneDRAW,oneLEAD,onePAY,oneROOF,PowerBI,Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website (NaN)',
    },
    {
      project: ' Content Management System',
      sent: 'oneBUILD,PA,3,Content Management System, Customer NaN Portal,oneBUILD, oneBUTTON, oneDOC, oneDRAW, oneLEAD,one PAY, oneROOF,PowerBI, Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website',
      passed: '',
      failed:
        'oneBUTTON,OBA,31,Content Management System,Customer Portal, oneBUILD, oneBUTTON,oneDOC, oneDRAW,oneLEAD,onePAY,oneROOF,PowerBI,Robotic Process Automation, SalesForce Maintenance, Skedulo, Trinity Website (NaN)',
    },
  ],
  teams: [
    {
      teamId: 1,
      name: 'Team 1',
      projectAndLead: 'Citrus - Dev - Shijith',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 123,
          members: [
            {
              memberId: 1000,
              name: 'Alif Shamsudheen',
              team: 'Citrus',
              role: 'Front End',
            },
            {
              memberId: 1001,
              name: 'Ambika Rajesh',
              team: 'Citrus',
              role: 'Back End',
            },
            { memberId: 1002, name: 'Jibi John', team: 'Citrus', role: 'Data' },
            {
              memberId: 1003,
              name: 'Shijith M V',
              team: 'Citrus',
              role: 'Full Stack',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 124,
          members: [
            {
              memberId: 1004,
              name: 'Phaniraja Sridhara',
              team: 'Application Development Team',
              role: 'App Dev',
            },
            {
              memberId: 1005,
              name: 'Praveen Deepala',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 125,
          members: [
            {
              memberId: 1006,
              name: 'Mathew Mullan',
              team: 'Business Application',
              role: 'UAM',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 126,
          members: [
            {
              memberId: 1007,
              name: 'Manu Goyal',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
      ],
      projects: [
        { projectId: 2000, name: 'oneBUTTON', priority: 1 },
        { projectId: 2001, name: 'oneDOC', priority: 2 },
        { projectId: 2002, name: 'oneUAM', priority: 3 },
        { projectId: 2003, name: 'G3 Monitoring', priority: 4 },
        { projectId: 2004, name: 'Apps & Permits', priority: 5 },
      ],
    },
    {
      teamId: 2,
      name: 'Team 2',
      projectAndLead: 'Citrus - Dev - Anu',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 127,
          members: [
            {
              memberId: 1008,
              name: 'Anu Sasidharan',
              team: 'Citrus',
              role: 'Full Stack',
            },
            { memberId: 1009, name: 'Jibi John', team: 'Citrus', role: 'Data' },
            {
              memberId: 1010,
              name: 'Sharath M V',
              team: 'Citrus',
              role: 'Front End',
            },
            {
              memberId: 1011,
              name: 'Sumina Mohammed',
              team: 'Citrus',
              role: 'Back End',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 128,
          members: [
            {
              memberId: 1012,
              name: 'Phaniraja Sridhara',
              team: 'Application Development Team',
              role: 'App Dev',
            },
            {
              memberId: 1013,
              name: 'Praveen Deepala',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 129,
          members: [
            {
              memberId: 1014,
              name: 'Bryan Bigica',
              team: 'Business Application',
              role: 'BA',
            },
            {
              memberId: 1015,
              name: 'Mathew Mullan',
              team: 'Business Application',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 130,
          members: [
            {
              memberId: 1016,
              name: 'Manu Goyal',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
      ],
      projects: [
        { projectId: 2005, name: 'oneROOF', priority: 1 },
        { projectId: 2006, name: 'Security Remediation', priority: 2 },
        { projectId: 2007, name: 'Procing Book', priority: 3 },
        { projectId: 2008, name: 'RRR', priority: 4 },
        { projectId: 2009, name: 'Sunnova Equipment Management', priority: 5 },
        { projectId: 2010, name: 'PhotoSync', priority: 6 },
        { projectId: 2011, name: 'Safety Cross', priority: 7 },
      ],
    },
    {
      teamId: 3,
      name: 'Team 3',
      projectAndLead: 'Citrus - Dev - Sumesh',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 131,
          members: [
            {
              memberId: 1017,
              name: 'Anju John',
              team: 'Citrus',
              role: 'Front End',
            },
            {
              memberId: 1018,
              name: 'Arunraj A',
              team: 'Citrus',
              role: 'Front End',
            },
            { memberId: 1019, name: 'Jibi John', team: 'Citrus', role: 'Data' },
            {
              memberId: 1020,
              name: 'Junaid P S',
              team: 'Citrus',
              role: 'Back End',
            },
            {
              memberId: 1021,
              name: 'Sumesh Gopinath',
              team: 'Citrus',
              role: 'Full Stack',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 132,
          members: [
            {
              memberId: 1022,
              name: 'Praveen Deepala',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 133,
          members: [
            {
              memberId: 1023,
              name: 'Bryan Bigica',
              team: 'Business Application',
              role: 'BA',
            },
            {
              memberId: 1024,
              name: 'Mathew Mullan',
              team: 'Business Application',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 134,
          members: [
            {
              memberId: 1025,
              name: 'Manu Goyal',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
      ],
      projects: [
        { projectId: 2012, name: 'Opportunity Contract', priority: 1 },
        { projectId: 2013, name: 'oneDRAW API', priority: 2 },
        { projectId: 2014, name: 'oneMAP API', priority: 3 },
        { projectId: 2015, name: 'Cobra', priority: 4 },
        { projectId: 2016, name: 'Excel Services', priority: 5 },
        { projectId: 2017, name: 'BT Dashboard', priority: 6 },
        { projectId: 2018, name: 'Genability', priority: 7 },
      ],
    },
    {
      teamId: 4,
      name: 'Team 4',
      projectAndLead: 'Citrus - Dev',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 135,
          members: [
            {
              memberId: 1026,
              name: 'Abdul Manaf',
              team: 'Citrus',
              role: 'Back End',
            },
            {
              memberId: 1027,
              name: 'Binumon George',
              team: 'Citrus',
              role: 'End',
            },
            { memberId: 1028, name: 'Jibi John', team: 'Citrus', role: 'Data' },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 136,
          members: [
            {
              memberId: 1029,
              name: 'Alias George',
              team: 'Application Development Team',
              role: 'App Dev',
            },
            {
              memberId: 1030,
              name: 'Benjamin Herman',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 137,
          members: [
            {
              memberId: 1031,
              name: 'Jeff Macdonald',
              team: 'Business Applications',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 138,
          members: [
            {
              memberId: 1032,
              name: 'John Veneziano',
              team: 'Data Team',
              role: 'Data',
            },
            {
              memberId: 1033,
              name: 'Manu Goyal',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
        {
          teamName: 'pmo',
          subTeamId: 139,
          members: [{ memberId: 1034, name: 'PMO', team: 'PMO', role: 'PMO' }],
        },
      ],
      projects: [{ projectId: 2019, name: 'onePAY', priority: 1 }],
    },
    {
      teamId: 5,
      name: 'Team 5',
      projectAndLead: 'Citrus - Dev',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 140,
          members: [
            {
              memberId: 1035,
              name: 'Anas Muhammed',
              team: 'Citrus',
              role: 'Back End',
            },
            { memberId: 1036, name: 'Jibi John', team: 'Citrus', role: 'Data' },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 141,
          members: [
            {
              memberId: 1037,
              name: 'Phaniraja Sridhara',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 142,
          members: [
            {
              memberId: 1038,
              name: 'Rick Lyons',
              team: 'Business Applications',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 143,
          members: [
            {
              memberId: 1039,
              name: 'Manu Goyal',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
        {
          teamName: 'pmo',
          subTeamId: 144,
          members: [],
        },
        {
          teamName: 'rpa',
          subTeamId: 145,
          members: [],
        },
      ],
      projects: [
        { projectId: 2020, name: 'RingCentral API', priority: 1 },
        { projectId: 2021, name: 'Document Review Engine', priority: 2 },
      ],
    },
    {
      teamId: 6,
      name: 'Team 6',
      projectAndLead: 'Citrus - Dev - Ryan',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 146,
          members: [
            {
              memberId: 1040,
              name: 'Rahul Sharma',
              team: 'Citrus',
              role: 'Front End',
            },
            {
              memberId: 1041,
              name: 'Neha Verma',
              team: 'Citrus',
              role: 'Back End',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 147,
          members: [
            {
              memberId: 1042,
              name: 'Michael Johnson',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 148,
          members: [
            {
              memberId: 1043,
              name: 'Ethan Carter',
              team: 'Business Applications',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 149,
          members: [
            {
              memberId: 1044,
              name: 'Olivia Clark',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
        {
          teamName: 'pmo',
          subTeamId: 150,
          members: [],
        },
      ],
      projects: [
        { projectId: 2022, name: 'oneCRM', priority: 1 },
        { projectId: 2023, name: 'oneHR', priority: 2 },
      ],
    },
    {
      teamId: 7,
      name: 'Team 7',
      projectAndLead: 'Citrus - Dev - Sarah',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 151,
          members: [
            {
              memberId: 1045,
              name: 'Jacob Lee',
              team: 'Citrus',
              role: 'Full Stack',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 152,
          members: [
            {
              memberId: 1046,
              name: 'Sophia Martinez',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 153,
          members: [
            {
              memberId: 1047,
              name: 'Daniel Evans',
              team: 'Business Applications',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 154,
          members: [
            {
              memberId: 1048,
              name: 'Emma Rodriguez',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
      ],
      projects: [
        { projectId: 2024, name: 'oneBuild', priority: 1 },
        { projectId: 2025, name: 'oneRisk', priority: 2 },
      ],
    },
    {
      teamId: 8,
      name: 'Team 8',
      projectAndLead: 'Citrus - Dev - Tom',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 155,
          members: [
            {
              memberId: 1049,
              name: 'William Brown',
              team: 'Citrus',
              role: 'Front End',
            },
            {
              memberId: 1050,
              name: 'Isabella Wilson',
              team: 'Citrus',
              role: 'Back End',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 156,
          members: [
            {
              memberId: 1051,
              name: 'Liam Thomas',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 157,
          members: [
            {
              memberId: 1052,
              name: 'Noah Scott',
              team: 'Business Applications',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 158,
          members: [
            {
              memberId: 1053,
              name: 'Ava Turner',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
        {
          teamName: 'rpa',
          subTeamId: 159,
          members: [
            { memberId: 1054, name: 'Automation', team: 'RPA', role: 'RPA' },
          ],
        },
      ],
      projects: [
        { projectId: 2026, name: 'oneData', priority: 1 },
        { projectId: 2027, name: 'oneAI', priority: 2 },
      ],
    },
    {
      teamId: 9,
      name: 'Team 9',
      projectAndLead: 'Citrus - Dev - Kevin',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 160,
          members: [
            {
              memberId: 1055,
              name: 'James Hall',
              team: 'Citrus',
              role: 'Front End',
            },
            {
              memberId: 1056,
              name: 'Charlotte White',
              team: 'Citrus',
              role: 'Back End',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 161,
          members: [
            {
              memberId: 1057,
              name: 'Benjamin Moore',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 162,
          members: [
            {
              memberId: 1058,
              name: 'Lucas Walker',
              team: 'Business Applications',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 163,
          members: [
            {
              memberId: 1059,
              name: 'Mia Green',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
      ],
      projects: [
        { projectId: 2028, name: 'oneCloud', priority: 1 },
        { projectId: 2029, name: 'oneInfra', priority: 2 },
      ],
    },
    {
      teamId: 10,
      name: 'Team 10',
      projectAndLead: 'Citrus - Dev - Daniel',
      TeamType: [
        {
          teamName: 'offshore',
          subTeamId: 164,
          members: [
            {
              memberId: 1060,
              name: 'Harper Adams',
              team: 'Citrus',
              role: 'Full Stack',
            },
          ],
        },
        {
          teamName: 'appDev',
          subTeamId: 165,
          members: [
            {
              memberId: 1061,
              name: 'Elijah Nelson',
              team: 'Application Development Team',
              role: 'App Dev',
            },
          ],
        },
        {
          teamName: 'ba',
          subTeamId: 166,
          members: [
            {
              memberId: 1062,
              name: 'Amelia Carter',
              team: 'Business Applications',
              role: 'BA',
            },
          ],
        },
        {
          teamName: 'data',
          subTeamId: 167,
          members: [
            {
              memberId: 1063,
              name: 'Mason Cooper',
              team: 'Data Team',
              role: 'Data',
            },
          ],
        },
      ],
      projects: [
        { projectId: 2030, name: 'oneFinance', priority: 1 },
        { projectId: 2031, name: 'oneInventory', priority: 2 },
      ],
    },
  ],
};
