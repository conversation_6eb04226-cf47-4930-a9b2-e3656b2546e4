import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { AuthService } from 'src/app/service/auth.service';
 
@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss'],
    standalone: false
})
export class DashboardComponent implements OnInit {
 
  constructor(private router: Router, private authService: AuthService) {
    router.events.subscribe((val) => {
      if (val instanceof NavigationEnd && this.checkIfDashboard()) {
        if (this.authService.onePMCapaability) { this.navigateOnePm(this.authService.onePMCapaability); }
      }
    });
  }
 
  ngOnInit(): void {
    this.authService.capabilitiesObservable.subscribe((capabilities: any) => {
      if (capabilities) {
        this.navigateOnePm(capabilities);
      }
    });
    this.navigateOnePm(this.authService.onePMCapaability);
  }
 
  navigateOnePm(capability: any) {
    if (this.checkIfDashboard() && capability) {
      if (capability?.App_Page != undefined) {
        if (localStorage.getItem('OnePmRole') === 'Change Management') {
          this.router.navigate(['/change-management-dashboard']);
          return;
        }
        // Check for 'Daily_Status_Report' first
        const dailyStatus = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'Daily_Status_Report' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (dailyStatus) {
          this.router.navigate(['/daily-dashboard']);
          return; // Exit after navigating
        }
 
        // Then check for 'Project_Level_Report'
        const projectLevel = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'Project_Level_Report' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (projectLevel) {
          this.router.navigate(['/project-dashboard']);
          return; // Exit after navigating
        }
 
        // Then check for 'PMO_Dashboard'
        const pmoDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'PMO_Dashboard' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (pmoDashboard) {
          this.router.navigate(['/pmo-dashboard']);
          return; // Exit after navigating
        }
       
        // Then check for 'KPI_Dashboard'
        const kpiDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'KPI_Dashboard' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (kpiDashboard) {
          this.router.navigate(['/kpi-dashboard']);
          return; // Exit after navigating
        }
 
        // Then check for 'Teams_Dashboard'
        const teamsDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'Teams_Dashboard' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (teamsDashboard) {
          this.router.navigate(['/teams-dashboard']);
          return; // Exit after navigating
        }
 
        // Then check for 'Utilization_Dashboard'
        const utilizationDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'Utilization_Dashboard' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (utilizationDashboard) {
          this.router.navigate(['/utilization-dashboard']);
          return; // Exit after navigating
        }
        // Then check for 'Jira_Timelog_Report'
        const jiraTimelogDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'Jira_Timelog_Report' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (jiraTimelogDashboard) {
          this.router.navigate(['/jira-timelog']);
          return; // Exit after navigating
        }
        // Then check for 'BA_Reporting_Dashboard'
        const baReportingDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'BA_Reporting_Dashboard' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (baReportingDashboard) {
          this.router.navigate(['/ba-reporting']);
          return; // Exit after navigating
        }
 
        // Then check for 'Change_Management_Dashboard'
        const changeManagementDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'Change_Management_Dashboard' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (changeManagementDashboard) {
          this.router.navigate(['/change-management-dashboard']);
          return; // Exit after navigating
        }
 
        // Then check for 'Executive_Dashboard'
        const executiveDashboard = capability.App_Page.find(
          (element: any) =>
            element.capability_name === 'Executive_Dashboard' &&
            element.capability_value === 'Operation_Allowed'
        );
        if (executiveDashboard) {
          this.router.navigate(['/executive-dashboard']);
          return; // Exit after navigating
        }
 
        // If neither exists, navigate to unauthorized
        this.router.navigate(['/unauthorized']);
      }
      else {
          // Handle case where App_Page is undefined
          this.router.navigate(['/unauthorized']);
      }
    }
  }
 
 
  checkIfDashboard(): boolean {
    const currentUrl = this.router.url;
    const lastPart = currentUrl.split('/').pop(); // Extract the last part of the URL
    return lastPart === '';
  }
}