<div>
    <div class="row mb-1">
        <app-project-dashboard-filter (emitFilters)="getFilterChange($event)"
            [excelData]="projectData"></app-project-dashboard-filter>
    </div>
    <div class="row">
        <app-project-dashboard-grid [gridData]="projectData" [projectFlag]="projectFlag"></app-project-dashboard-grid>
    </div>
</div>
<div class="sticky-buttons-group">
    <button nz-button nzType="primary" *ngIf="!isDivVisible" (click)="toggleDiv()" class="sticky-btn"
        matTooltip="Status Key" matTooltipPosition="after"><span><i class="fa fa-info-circle"></i></span></button>
    <div class="toggle-div" *ngIf="isDivVisible">
        <button (click)="toggleDiv()" class="close"><span><i class="fa fa-times"></i></span></button>
        <h6 class="status-indicator-head">Legends</h6>
        <ul *ngIf="getLegendsLength()">
            <li *ngFor="let status of getLegends()"
                [ngStyle]="{'background-color': status.backgroundColor,'color':status.fontColor,'border': '2px solid', 'borderColor': status.borderColor}">
                {{status.category}}</li>
        </ul>
    </div>
</div>