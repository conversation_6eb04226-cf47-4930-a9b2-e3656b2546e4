header {
  background-color: #538ABC !important;
}

.header-contents {
  display: flex;
  justify-content: space-between;
}

#app-name {
  margin: 0px;
  margin-left: 10px;
  border-left: 1px solid;
  padding-left: 10px;
  color: #dadbdf;
  font-size: 22px;
  font-weight: 500;
}

#main-logo {
  max-width: 80px;
  filter: contrast(100) invert(1);
}

#app-logo {
  height: 37.26px;
}

.dropdown-item:hover {
  text-decoration: none;
}

.dropdown-menu {
  position: absolute;
  top: 100% !important;
}

.fixClass {
  position: fixed;
  width: 100vw;
  z-index: 1001;
  top: 0;
  min-height: 56px;
}

a {
  cursor: pointer;
}

.dropdown-item {
  background-color: white;
}

.header-btn {
  height: 30px;
  padding: 3px;
}

#app-switching-button, #onePMMenu {
  border: 1px solid #A9CBE5 !important;
  border-radius: 6px !important;
}

.top-nav-menu-container {
  display: flex;
}

.top-nav-menu-btn {
  width: 40px !important;
  height: 40px !important;
  padding: 5px 0px 5px 5px;
  margin-right: 8px;
  background-color: #538ABC !important;
}

.btn-active {
  background-color: #A9CBE5 !important;
}

.btn-inactive {
  background-color: #538ABC !important;
}

.top-nav-menu-container img {
  width: 22px;
  padding: 0;
}

.top-nav-profile-sec {
  position: relative;
  display: flex;
  margin-top: 4px;
  // margin-left: 12px;
}

.list-selected {
  color: #4879a5 !important;
}

.list-unselected {
  color: #A9CBE5 !important;
}

.one-perm-icon {
  margin-right: 3px;
}

#onePERMMenu {
  border: 1px solid #A9CBE5 !important;
  border-radius: 6px !important;
  text-align: center;
}

.apps-menu {
  position: relative;
  display: flex;
    .dropdown-menu {
      padding: 1px;
      left: auto !important;
      font-size: 14px;
    }
}


.dropdown-item.active,
.dropdown-item:active {
  color: #216494 !important;
  background: #A9CBE5 !important;
}

.dropdown-item:not(.active):hover {
  color: #212530 !important;
  background: #e9ecef !important;
}

.dash-icon {
  padding-right: 5px;
  color: #A9CBE5
}

.ant-breadcrumb {
  background-color: #c2d4e4;
  // padding: 0px 22px 0px 28px;
  padding-left: 15px;
  font-size: 12px;
  line-height: 2.2;
  font-weight: 600;
  width: 100%;
  position: fixed;
  margin-top: -37px;
  top: 93px;
  z-index: 99;
  left: 0;

  .anticon {
    font-size: 12px;
    vertical-align: 0;
  }

  a {
    text-decoration: none;
  }

  a:hover {
    color: #337ab7;
  }
}

.ant-breadcrumb>:last-child a,
.ant-btn {
  pointer-events: none;
}

.ant-breadcrumb>:first-child a,
.ant-btn {
  pointer-events: none;
}

.from-header {
  margin-bottom: 12px;
}

.disable-hover {
  pointer-events: none !important;
}

.ico-align {
  padding-right: 5px;
  padding-top: 5px;
  color: white;
  font-size: 17px;
}

.btn-active .ico-align {
  color: #4879a5 !important;
}