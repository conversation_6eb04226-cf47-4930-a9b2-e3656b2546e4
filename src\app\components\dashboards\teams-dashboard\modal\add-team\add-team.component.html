<div class="container-fluid">
    <form [formGroup]="teamForm">
        <div class="row mb-2">
            <label for="teamName" class="mb-0 col-sm-5 col-form-label">Team Name <span class="text-danger">*</span></label>
            <div class="col-sm-7">
                <input type="text" class="form-control" id="teamName" placeholder="Enter Team Name" autocomplete="off" formControlName="teamName" autocomplete="off"/>
            </div>
        </div>
        <div class="row">
            <label for="teamDescription" class="mb-0 col-sm-5 col-form-label">Team Description <span class="text-danger">*</span></label>
            <div class="col-sm-7">
                <input class="form-control" id="teamDescription" placeholder="Team Description" autocomplete="off" formControlName="teamDescription"/>
            </div>
        </div>
    </form>
</div>
<ng-template #modalFooterTemplate let-modal>
    <button type="button" nz-button nzType="default" class="me-1" (click)="resetForm()"><span><i class="fa fa-refresh fa-solid me-2"></i></span>Reset</button>
    <button type="submit" nz-button nzType="primary" class="custom-pad-btn" [disabled]="teamForm.invalid" (click)="onSubmit()">
        <span><i class="fa fa-floppy-o fa-solid me-2"></i></span>Save
    </button>
</ng-template>