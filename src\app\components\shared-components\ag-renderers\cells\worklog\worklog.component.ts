import { Component } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
    selector: 'app-worklog',
    templateUrl: './worklog.component.html',
    styleUrls: ['./worklog.component.scss'],
    standalone: false
})
export class WorklogComponent implements AgRendererComponent {
  params: any;
  cellValue: any;
  constructor(private modalService: NzModalService) {}
  agInit(params: ICellRendererParams): void {
    this.params = params;    
    this.cellValue = this.getValueToDisplay(params);
  }
  refresh(params: ICellRendererParams): boolean {
    this.cellValue = this.getValueToDisplay(params);
    return true;
  }
  getValueToDisplay(params: ICellRendererParams) {
    return params.valueFormatted ? params.valueFormatted : params.value;
  }
 
  showPopup(contents:any): void {
    this.modalService.create({
      nzTitle: this.params.data?.epic,
      nzContent:contents,
      nzOkText: 'Close',
      nzCancelText:null,
      nzClosable: true,
      nzMask:false,
      nzBodyStyle: { padding: '5px'}
    });
  }
}