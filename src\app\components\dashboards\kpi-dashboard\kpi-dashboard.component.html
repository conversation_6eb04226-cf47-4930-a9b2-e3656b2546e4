<div>
    <div class="row mb-1">
        <app-kpi-filter (emitFilters)="getFilterChange($event)" [gridData]="gridData"
            [gridMode]="gridMode"></app-kpi-filter>
    </div>
    <div class="row">
        <app-hours-and-tickets-grid *ngIf="gridMode == 'Tickets_And_Hours_By_Project'" [gridData]="ticketHoursGridData"
            [chartData]="hoursChartData" [hoursandCountData]="hoursandCountData"></app-hours-and-tickets-grid>
        <app-hours-analysis-by-team *ngIf="gridMode == 'Hours_Analysis'" [startDate]="fromDate" [endDate]="toDate"
            [gridData]="projectSummaryGrid" [teamSummaryGridData]="teamSummaryGrid"
            [growthSummaryGridData]="growthSummaryGrid"
            [jiraSummaryGridData]="jiraSummaryGridData"></app-hours-analysis-by-team>
        <app-kpi-key-features *ngIf="gridMode == 'Key_Features'" [gridData]="keyFeatureGridData"
            [footerDetails]="keyFeatureloggedData"></app-kpi-key-features>
        <app-kpi-deployment-success *ngIf="gridMode == 'Deployment_Success'" [kpiProdSuccessData]="prodSuccessGridData"
            [kpiUatSuccessData]="uatSuccessGridData"></app-kpi-deployment-success>
    </div>
</div>