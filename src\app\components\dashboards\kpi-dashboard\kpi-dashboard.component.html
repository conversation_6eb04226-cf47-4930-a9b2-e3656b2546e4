<div>
  <div class="row mb-1">
    <app-kpi-filter (emitFilters)="getFilterChange($event)" [gridData]="gridData"
    [gridMode]="gridMode"></app-kpi-filter>
  </div>
  <div class="row">
    @if (gridMode == 'Tickets_And_Hours_By_Project') {
      <app-hours-and-tickets-grid [gridData]="ticketHoursGridData"
      [chartData]="hoursChartData" [hoursandCountData]="hoursandCountData"></app-hours-and-tickets-grid>
    }
    @if (gridMode == 'Hours_Analysis') {
      <app-hours-analysis-by-team [startDate]="fromDate" [endDate]="toDate"
        [gridData]="projectSummaryGrid" [teamSummaryGridData]="teamSummaryGrid"
        [growthSummaryGridData]="growthSummaryGrid"
      [jiraSummaryGridData]="jiraSummaryGridData"></app-hours-analysis-by-team>
    }
    @if (gridMode == 'Key_Features') {
      <app-kpi-key-features [gridData]="keyFeatureGridData"
      [footerDetails]="keyFeatureloggedData"></app-kpi-key-features>
    }
    @if (gridMode == 'Deployment_Success') {
      <app-kpi-deployment-success [kpiProdSuccessData]="prodSuccessGridData"
      [kpiUatSuccessData]="uatSuccessGridData"></app-kpi-deployment-success>
    }
  </div>
</div>