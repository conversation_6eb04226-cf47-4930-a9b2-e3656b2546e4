import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';

@Component({
    selector: 'app-add-member',
    templateUrl: './add-member.component.html',
    styleUrls: ['./add-member.component.scss'],
    standalone: false
})
export class AddMemberComponent implements OnInit {
  @Input() data: any;
  @ViewChild('modalFooterTemplate', { static: true }) modalFooterTemplate!: TemplateRef<any>;
  memberData: any[]=[];
  categoryData: any[]=[];
  memberForm!: FormGroup;
    constructor(
      private formBuilder: FormBuilder,
      private onePmService: OnepmServiceService,
      private notification: NzNotificationService,
      private modalRef: NzModalRef<AddMemberComponent>,
      private loaderService: LoaderService,
    ) { }

  ngOnInit(): void {
    this.initializeForm(); 
    this.memberData = this.data.memberList;
    this.categoryData = this.data.categoryList;
  }
  /**
   * Function is used to initialize form 
   */
  initializeForm(): FormGroup {
    return this.memberForm = this.formBuilder.group({
      memberId:[null,[Validators.required]],
      category:[null,[Validators.required]],
      skill:['',[Validators.required,Validators.pattern(/^(?!\s*$).+/)]],
    });
  }
  /**
   * Function for submit 
   */
  onSubmit(){
    if (this.memberForm.valid) {
      this.loaderService.invokeLoaderComponent(true);
      let userId = localStorage.getItem('userEmail');
      let body = {
        teamId:this.data.teamId,
        memberId: +this.memberForm.value.memberId,
        category: this.memberForm.value.category,
        skills:this.memberForm.value.skill.trim(),
        userId: userId
      };
      this.onePmService.addTeamMemberData(body).subscribe(
        (res) => {
          this.loaderService.invokeLoaderComponent(false);
          if(res.succeeded){
          this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'success' });
          this.resetForm();
          this.modalRef.close(true); 
          }  else {
            this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
          }
        },
        (error) => {
          this.loaderService.invokeLoaderComponent(false);
          this.modalRef.close(false); 
          this.notification.blank('', error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      );
    } else {
      this.notification.blank('', 'Fill the Required Fields', { nzPlacement: 'bottomLeft', nzClass: 'error' });
    }
  }
  /**
   * Function for reset 
   */
  onReset(){
    this.resetForm();
  }
  /**
   * Function for reset form 
   */
  resetForm(): void {
    this.memberForm.reset();
  }
}
