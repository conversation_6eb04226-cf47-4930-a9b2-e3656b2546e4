import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProjectDashboardFilterComponent } from './project-dashboard-filter.component';

describe('ProjectDashboardFilterComponent', () => {
  let component: ProjectDashboardFilterComponent;
  let fixture: ComponentFixture<ProjectDashboardFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ProjectDashboardFilterComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ProjectDashboardFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
