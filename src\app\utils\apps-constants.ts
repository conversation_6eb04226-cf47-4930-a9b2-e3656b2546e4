export const dashboardConstants = {
    defaultColDef: {
      sortable: true,
      resizable: true,
      suppressMenuHide: true,
      minWidth: 195,
    },
    // Common grid options for all AG Grid instances
    gridOptions: {
      theme: "legacy" as const // Use legacy theming system to avoid conflicts with CSS files
    },
    noRowsTemplate: `<span>No records found</span>`,
    gridStyle: `height: calc(100vh - 160px) !important; width: 100%;`,
    gridStyleAccordian: `height: calc(30vh) !important; width: 100%;`,
    dailydashboardColDef:[
      {
        headerName: 'Project',
        field: 'project',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 100,
        width: 100,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: 'Key',
        field: 'epic',
        cellRenderer: 'jiraIdRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 100,
        width: 100,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },  
      {
        headerName: 'Description',
        field: 'description',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 350,
        width: 350,
        wrapText:true,
        autoHeight: true,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: "Work Description",
        field: 'workDescriptions',
        cellRenderer: 'worklogRenderer',
        filter: 'textFilter',
        sortable: true,
        headerComponent: 'normalHeader',
        minWidth: 250,
        width: 250,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Status',
        field: 'status',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 200,
        width: 200,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: '#Hrs',
        field: 'workLogInfo',
        cellRenderer: 'hourRenderer',
        valueGetter: (params: any) => params.data.workLogInfo?.hours,
        filter: 'textFilter',
        sortable: true,
        headerClass: 'ag-center-header',
        minWidth: 100,
        width: 100,
        flex: 1,  
        cellClass: 'cell-center',
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.workLogInfo.backgroundColor,
            color: params.data.workLogInfo.fontColor,
            border: params.data.workLogInfo.borderColor ? '2px solid '+params.data.workLogInfo.borderColor+'':''
          };
      },
      }, 
      {
        headerName: 'Remaining',
        field: 'remaining',
        cellRenderer: 'hourRenderer',
        filter: 'textFilter',
        sortable: true,
        headerClass: 'ag-center-header',
        minWidth: 100,
        width: 100,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
        cellClass: 'cell-center',
      }, 
      {
        headerName: '%Hrs Complete',
        field: 'hoursCompleted',
        cellRenderer: 'percentageRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
        cellClass: 'cell-center',
        headerClass: 'ag-center-header',
      },      
    ],
    projectColDef:[
      {
        headerName: 'Key',
        field: 'issueKey',
        cellRenderer: 'jiraIdRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 100,
        width: 100,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },  
      {
        headerName: 'Description',
        field: 'description',
        cellRenderer: 'descRenderer',
        sortable: true,
        minWidth: 350,
        width: 350,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
        wrapText:true,
        autoHeight: true,
      },
      {
        headerName: 'Assignee',
        field: 'assignee',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 200,
        width: 200,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: "Start Date",
        field: 'startDate',
        cellRenderer: 'dateRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 120,
        width: 120,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: 'Due Date',
        field: 'dueDateInfo',
        cellRenderer: 'dateInfoRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 120,
        width: 120,
        flex: 1,
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.dueDateInfo.backgroundColor,
            color: params.data.dueDateInfo.fontColor,
            border: params.data.dueDateInfo.borderColor ? '2px solid '+params.data.dueDateInfo.borderColor+'':''
          };
      },
        // cellStyle: { 'user-select': 'text'},
        cellClass: 'cell-left',
      },
      {
        headerName: 'Status',
        field: 'developmentStage',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 190,
        width: 190,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text'}
      },
      {
        headerName: '#Hrs',
        field: 'workLogInfo',
        cellRenderer: 'hourInfoRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 100,
        width: 100,
        flex: 1,
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.workLogInfo.backgroundColor,
            color: params.data.workLogInfo.fontColor,
            border: params.data.workLogInfo.borderColor ? '2px solid '+params.data.workLogInfo.borderColor+'':''
          };
      },
        // cellStyle: { 'user-select': 'text' },
        cellClass: 'cell-center',
        headerClass: 'ag-center-header',
      },
      {
        headerName: 'Remaining',
        field: 'remainingHours',
        cellRenderer: 'hourRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 120,
        width: 120,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
        cellClass: 'cell-center',
        headerClass: 'ag-center-header',
      },
      {
        headerName: '% Hrs Complete',
        field: 'remainingHoursPercentage',
        cellRenderer: 'percentageRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
        cellClass: 'cell-center',
        headerClass: 'ag-center-header',
      },      
    ],
    pmoPastDueDateColDef:[
      {
        headerName: 'Project',
        field: 'project',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: 'Key',
        field: 'issueKey',
        cellRenderer: 'jiraIdRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 50,
        width: 50,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },  
      {
        headerName: 'Summary',
        field: 'summary',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 500,
        width: 500,
        wrapText:true,
        autoHeight: true,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Assignee',
        field: 'assignee',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 200,
        width: 200,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Development Stage',
        field: 'developmentStage',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 250,
        width: 250,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: 'Due Date',
        field: 'dueDateInfo',
        cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.dueDateInfo?.date,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
        sortable: true,
        minWidth: 80,
        width: 80,
        flex: 1,
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.dueDateInfo.backgroundColor,
            color: params.data.dueDateInfo.fontColor,
            border: params.data.dueDateInfo.borderColor ? '2px solid '+params.data.dueDateInfo.borderColor+'':''
          };
      },
      cellClass: 'cell-left',
      }, 
    ],
    pmoMissingDueDateColDef:[
      {
        headerName: 'Project',
        field: 'project',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: 'Key',
        field: 'issueKey',
        cellRenderer: 'jiraIdRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 50,
        width: 50,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },  
      {
        headerName: 'Summary',
        field: 'summary',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 500,
        width: 500,
        wrapText:true,
        autoHeight: true,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Assignee',
        field: 'assignee',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 200,
        width: 200,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Development Stage',
        field: 'developmentStage',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 200,
        width: 200,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
    ],
    pmoMissingEstimateColDef:[
      {
        headerName: 'Project',
        field: 'project',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: 'Key',
        field: 'issueKey',
        cellRenderer: 'jiraIdRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 50,
        width: 50,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },  
      {
        headerName: 'Summary',
        field: 'summary',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 500,
        width: 500,
        wrapText:true,
        autoHeight: true,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Assignee',
        field: 'assignee',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 200,
        width: 200,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Development Stage',
        field: 'developmentStage',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 200,
        width: 200,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
    ],
    pmoRiskColDef:[
      {
        headerName: 'Project',
        field: 'project',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 100,
        width: 100,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },
      {
        headerName: 'Key',
        field: 'issueKey',
        cellRenderer: 'jiraIdRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 100,
        width: 100,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      },  
      {
        headerName: 'Summary',
        field: 'summary',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 300,
        width: 300,
        wrapText:true,
        autoHeight: true,
        flex: 1,
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Assignee',
        field: 'assignee',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Development Stage',
        field: 'developmentStage',
        cellRenderer: 'normalRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 180,
        width: 180,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: { 'user-select': 'text' },
      }, 
      {
        headerName: 'Due Date',
        field: 'dueDateInfo',
        cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.dueDateInfo?.date,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
        sortable: true,
        minWidth: 120,
        width: 120,
        flex: 1,
        cellClass: 'cell-left',
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.dueDateInfo.backgroundColor,
            color: params.data.dueDateInfo.fontColor,
            border: params.data.dueDateInfo.borderColor ? '2px solid '+params.data.dueDateInfo.borderColor+'':''
          };
      },
      }, 
      {
        headerName: 'Estimate',
        field: 'estimate',
        cellRenderer: 'hourRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 120,
        width: 120,
        flex: 1,
        cellClass: 'cell-center',
        cellStyle: { 'user-select': 'text' },
        headerClass: 'ag-center-header',
      }, 
      {
        headerName: 'Spent',
        field: 'workLogInfo',
        valueGetter: (params: any) => params.data.workLogInfo?.hours,
        cellRenderer: 'hourRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 100,
        width: 100,
        flex: 1,
        cellClass: 'cell-center',
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.workLogInfo.backgroundColor,
            color: params.data.workLogInfo.fontColor,
            border: params.data.workLogInfo.borderColor ? '2px solid '+params.data.workLogInfo.borderColor+'':''
          };
      },
      headerClass: 'ag-center-header',
      }, 
      {
        headerName: 'Effort Remaining',
        field: 'remainingEffortInfo',
        valueGetter: (params: any) => params.data.remainingEffortInfo?.hours,
        cellRenderer: 'hourRenderer',
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellClass: 'cell-center',
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.remainingEffortInfo.backgroundColor,
            color: params.data.remainingEffortInfo.fontColor,
            border: params.data.remainingEffortInfo.borderColor ? '2px solid '+params.data.remainingEffortInfo.borderColor+'':''
          };
      },
      }, 
      {
        headerName: 'Time Remaining',
        field: 'remainingTimeInfo',
        cellRenderer: 'hourRenderer',
        valueGetter: (params: any) => params.data.remainingTimeInfo?.hours,
        filter: 'textFilter',
        sortable: true,
        minWidth: 150,
        width: 150,
        flex: 1,
        cellClass: 'cell-center',
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.remainingTimeInfo.backgroundColor,
            color: params.data.remainingTimeInfo.fontColor,
            border: params.data.remainingTimeInfo.borderColor ? '2px solid '+params.data.remainingTimeInfo.borderColor+'':''
          };
      },
      }, 
    ],
    ticketsAndHours:[
      { headerName: "Projects/Initiatives", field: "project",minWidth: 450,
        width: 450, flex:1},
      { headerName: "#Tickets", field: "ticketsCount",minWidth: 200,
        width: 200, flex:1,cellStyle:{textAlign: 'center'},
        headerClass: 'ag-center-header',
      },
      { headerName: "Total Time (in hours)", field: "totalHours",minWidth: 200,
        cellStyle:{textAlign: 'center'},
        width: 200, flex:1,headerClass: 'ag-center-header',
      },
      { headerName: "Percentage", field: "percentage",minWidth: 200,cellRenderer:'percentageRenderer',
        width: 200, flex:1,
        cellStyle:{textAlign: 'center'},
        headerClass: 'ag-center-header',
      },
    ],
    kpiHoursAnalysisByTeam: [
      {
        headerName:
          "Planned/Unplanned and RUN/GROW/TRANSFORM hours for BA, App Dev and Data Teams",
        marryChildren: true,
        headerClass: 'header-center',
        headerStyle: { textAlign: 'center' },
        flex: 1,
        minWidth: 190,
        children: [
          {
            headerName: "Projects/Initiatives",
            field: "projectName",
            wrapText:true,
            autoHeight:true,
            rowSpan: (params: any) => (params.node.rowIndex === 0 ? 2 : 1),
            flex: 1,
            minWidth: 150, width: 150,
          },
          // Planned Hours Group
          {
            headerName: "Planned Hours",
            marryChildren: true,
            flex: 1,
            children: [
        // cellClass: 'cell-center',
        { headerName: "BA",  field: "planned.ba",headerClass: 'wrap-header',cellClass: 'cell-center', valueGetter: (params:any) => typeof params.data?.planned?.ba == 'number' ? params.data.planned.ba.toFixed(2): params.data.planned.ba, minWidth: 100, width: 100, flex: 1,cellStyle:{textAlign: 'center'},},
              { headerName: "App Dev", field: "planned.appDev",headerClass: 'wrap-header',cellClass: 'cell-center', valueGetter: (params:any) => typeof params.data?.planned?.appDev == 'number' ?params.data?.planned?.appDev.toFixed(2):params.data?.planned?.appDev, minWidth: 100, width: 100, flex: 1,cellStyle:{textAlign: 'center'} },
              { headerName: "Data", field: "planned.data",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => typeof params.data?.planned?.data == 'number' ? params.data?.planned?.data.toFixed(2): params.data?.planned?.data, minWidth: 100, width: 100, flex: 1,cellStyle:{textAlign: 'center'} },
              { headerName: "RPA", field: "planned.rpa",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => typeof params.data?.planned?.data == 'number' ? params.data?.planned?.rpa.toFixed(2): params.rpa?.planned?.rpa, minWidth: 100, width: 100, flex: 1,cellStyle:{textAlign: 'center'} }
            ]
          },
          // Unplanned Hours Group
          {
            headerName: "Unplanned Hours",
            marryChildren: true,
            flex: 1,
            children: [
              { headerName: "BA", field: "unplannedBA",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => typeof params.data?.unPlanned?.ba == 'number' ? params.data.unPlanned.ba.toFixed(2): params.data.unPlanned.ba, minWidth: 100, width: 100,  flex: 1,cellStyle:{textAlign: 'center'} },
              { headerName: "App Dev", field: "unplannedAppDev",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => typeof params.data?.unPlanned?.appDev == 'number' ? params.data.unPlanned.appDev.toFixed(2): params.data.unPlanned.appDev, minWidth: 100, width: 100,  flex: 1,cellStyle:{textAlign: 'center'} },
              { headerName: "Data", field: "unplannedData",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => typeof params.data?.unPlanned?.data == 'number' ? params.data.unPlanned.data.toFixed(2): params.data.unPlanned.data, minWidth: 100, width: 100,  flex: 1,cellStyle:{textAlign: 'center'} },
              { headerName: "RPA", field: "unplannedRPA",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => typeof params.data?.unPlanned?.rpa == 'number' ? params.data.unPlanned.rpa.toFixed(2): params.data.unPlanned.rpa, minWidth: 100, width: 100,  flex: 1,cellStyle:{textAlign: 'center'} }
            ]
          },
          // Total Hours Group
          {
            headerName: "Total Hours",
            marryChildren: true,
            flex: 1,//totalHours
            children: [
              { headerName: "Total Planned Hours", field: "totalPlannedHours",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => params.data?.totalHours?.totalPlanned.toFixed(2), minWidth: 160, width: 160,  flex: 1,cellStyle:{textAlign: 'center'} },
              { headerName: "Total Unplanned Hours", field: "totalUnplannedHours",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => params.data?.totalHours?.totalUnPlanned.toFixed(2), minWidth: 170, width: 170,  flex: 1,cellStyle:{textAlign: 'center'} },
              { headerName: "Total Hours", field: "totalHours",headerClass: 'wrap-header',cellClass: 'cell-center',valueGetter: (params:any) => params.data?.totalHours?.totalHours.toFixed(2),  minWidth: 120, width: 120,  flex: 1,cellStyle:{textAlign: 'center'} }
            ]
          }
        ]
      }
    ],  
    kpiKeyFeatures: [
      {
        headerName:
          "Key Features (top 20 tickets with highest time logged per project)",
        marryChildren: true, // Ensures all child columns stay together
        headerClass: 'header-center',
        headerStyle: { textAlign: 'center' },
        children: [
          {
            headerName: "Ticket",
            field: "ticketKey",
            sortable: false,
            width: 150,            
            minWidth: 150,  
            flex:1,          
            cellRendererSelector: (params: any) => {
              if (params.data.isMerged || params.value =='Total') {
                return { component: 'normalRenderer' };
              } else {
                return { component: 'jiraIdRenderer' };
              }
            },
            valueGetter: (params: any) => params.data.isMerged ? params.data.project : params.data.ticketKey,
            cellClass: (params: any) => params.data.isMerged ? 'merged-header cell-center' : '',
            colSpan: (params: any) => {
              const haveMergedRows = params.data.isMerged;
              if (haveMergedRows) {
                return 3;
              } else {
                return 1;
              }
            },
          },
          {
            headerName: "Description",
            field: "ticketDescription",
            sortable: false,
            width: 550,  
            minWidth: 550,            
            flex: 4,
          },
          {
            headerName: "Logged Hours",
            field: "hours",
            type: "numericColumn",
            sortable: false,
            width: 150,    
            minWidth: 150,            
            flex: 1,
            cellStyle:{textAlign: 'center'},
            headerClass: 'ag-center-header',
          }
        ],        
      }
    ],

    kpiUATSuccess:[
      {
        headerName:"UAT Testing Success Rate",
        marryChildren: true, // Ensures all child columns stay together
        headerClass: 'header-center',
        headerStyle: { textAlign: 'center' },
        children: [
          {
            headerName: 'Project',
            field: 'project',
            cellRenderer: 'normalRenderer',
            filter: 'textFilter',
            sortable: true,
            minWidth: 150,
            width: 150,
            wrapText: true,
            autoHeight: true,
            headerClass: 'wrap-header',
            flex: 1,
            cellStyle: { 'user-select': 'text' },
          },
          {
            headerName: 'Tickets Sent to UAT Testing',
            field: 'uatCount',
            cellRenderer: 'normalRenderer',
            filter: 'textFilter',
            sortable: true,
            minWidth: 150,
            width: 150,
            wrapText: true,
            autoHeight: true,
            headerClass: 'wrap-header',
            flex: 1,
            cellStyle: { textAlign:'center' },
          },
          {
            headerName: 'Tickets Passed UAT Testing',
            field: 'passedUATCount',
            cellRenderer: 'normalRenderer',
            filter: 'textFilter',
            sortable: true,
            minWidth: 150,
            width: 150,
            wrapText: true,
            autoHeight: true,
            headerClass: 'wrap-header',
            flex: 1,
            cellStyle: {  textAlign:'center' },
          },
          {
            headerName: 'Tickets Failed UAT Testing',
            field: 'failedUATCount',
            cellRenderer: 'normalRenderer',
            filter: 'textFilter',
            sortable: true,
            minWidth: 150,
            width: 150,
            wrapText: true,
            autoHeight: true,
            headerClass: 'wrap-header',
            flex: 1,
            cellStyle: {  textAlign:'center' },
          },
      
        ],
      }
    ],
     
    kpiProdSuccess:[
    {
      headerName:"PROD Testing Success Rate",
      marryChildren: true, // Ensures all child columns stay together
      headerClass: 'header-center',
      headerStyle: { textAlign: 'center' },
      children:[
        {
          headerName: 'Project',
          field: 'project',
          cellRenderer: 'normalRenderer',
          filter: 'textFilter',
          sortable: true,
          minWidth: 150,
          width: 150,
          wrapText: true,
          headerClass: 'wrap-header',
          autoHeight: true,
          flex: 1,
        },
        {
          headerName: 'Tickets Sent to Prod Testing',
          field: 'prodCount',
          cellRenderer: 'normalRenderer',
          filter: 'textFilter',
          sortable: true,
          minWidth: 150,
          width: 150,
          wrapText: true,
          autoHeight: true,
          headerClass: 'wrap-header',
          flex: 1,
          cellStyle: {  textAlign:'center' },
        },
        {
          headerName: 'Tickets Passed Prod Testing',
          field: 'passedPRODCount',
          cellRenderer: 'normalRenderer',
          filter: 'textFilter',
          sortable: true,
          minWidth: 150,
          width: 150,
          wrapText: true,
          autoHeight: true,
          headerClass: 'wrap-header',
          flex: 1,
          cellStyle: {  textAlign:'center' },
        },
        {
          headerName: 'Tickets Failed Prod Testing',
          field: 'failedPRODCount',
          cellRenderer: 'normalRenderer',
          filter: 'textFilter',
          sortable: true,
          minWidth: 150,
          width: 150,
          wrapText: true,
          autoHeight: true,
          headerClass: 'wrap-header',
          flex: 1,
          cellStyle: {  textAlign:'center'},
        },
    
      ],
    }
    ],

    teamSummaryColDef:[ {
      field: 'team', headerName: 'Team', minWidth: 70,
      width: 70,flex:1,wrapText: true,autoHeight: true,
      headerClass: 'wrap-header',
    },
    {
      field: 'plannedHours', headerName: 'Planned Hours', type: 'numericColumn', minWidth: 70,cellStyle: { textAlign: 'center' },
      width: 70,flex:1,headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center',
    },
    {
      field: 'plannedPercentage', headerName: 'Planned %', minWidth: 70,cellStyle: { textAlign: 'center' },
      width: 70,flex:1,headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center',
    },
    {
      field: 'unPlannedHours', headerName: 'Unplanned Hours', type: 'numericColumn', minWidth: 90,cellStyle: { textAlign: 'center' },
      width: 90,flex:1,headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center',
    },
    {
      field: 'unPlannedPercentage', headerName: 'Unplanned %', minWidth: 100,cellStyle: { textAlign: 'center' },
      width: 90,flex:1,headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center',
      valueFormatter: (params: any) => {
        const value = params.value;
        return typeof value === 'number' ? value.toFixed(2) : value || ''; // gracefully handle strings or empty
        }  
    },
    {
      field: 'totalHours', headerName: 'Total Hours', type: 'numericColumn', minWidth: 80,cellStyle: { textAlign: 'center' },
      width: 80,flex:1,headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center',
    },],
    growthSummaryColDef:[
      { field: 'team', headerName: 'Team',headerClass: 'wrap-header', width:70, minWidth:70,wrapText: true,autoHeight: true },
      { field: 'runHours', headerName: 'Run Hours', type: 'numericColumn', width:90, minWidth:90,cellStyle: { textAlign: 'center' },headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center', },
      { field: 'runPercentage', headerName: 'Run (%)', width:70, minWidth:70,cellStyle: { textAlign: 'center' } ,headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center', },
      { field: 'growthHours', headerName: 'Growth (H)', type: 'numericColumn', width:90, minWidth:90,cellStyle: { textAlign: 'center' },headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center',   },
      { field: 'growthPercentage', headerName: 'Growth (%)', width:70, minWidth:70,cellStyle: { textAlign: 'center' },wrapText: true,autoHeight: true,cellClass: 'cell-center',  },
      { field: 'transformHours', headerName: 'Transform (H)', type: 'numericColumn', width:120, minWidth:120,cellStyle: { textAlign: 'center' },headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center', },
      { field: 'transformPercentage', headerName: 'Transform (%)', width:120, minWidth:120,cellStyle: { textAlign: 'center' },headerClass: 'wrap-header',wrapText: true,autoHeight: true, cellClass: 'cell-center',
      valueFormatter: (params: any) => {
        const value = params.value;
        return typeof value === 'number' ? value.toFixed(2) : value || ''; // gracefully handle strings or empty
        }    
    },
      { field: 'totalHours', headerName: 'Total (H)', type: 'numericColumn', width:90, minWidth:90,cellStyle: { textAlign: 'center' },headerClass: 'wrap-header',wrapText: true,autoHeight: true,cellClass: 'cell-center', },
    ],
    jiraSummaryColDef:[
      { field: 'team', headerName: 'Team', width:90, minWidth:90,wrapText: true,autoHeight: true },
      { field: 'jiraTicketCount', headerName: 'JIRA Tickets', type: 'numericColumn', width:130, minWidth:130,cellStyle: { textAlign: 'center' },cellClass: 'cell-center', valueFormatter: (params: any) => params.value != null ? params.value.toFixed(2) : '' },
    ],
    ultilizationColdef:[
      { field: 'memberName', headerName: 'Name', minWidth: 150, flex: 1, sortable: true, filter: true },
      { field: 'employeeType', headerName: 'Employee Type', minWidth: 130, flex: 1,},
      { field: 'totalMTDHours', headerName: 'Total MTD Hours', minWidth: 120, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header', },
      { field: 'businessDaysMTD', headerName: 'Business Days MTD', minWidth: 150, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header'},
      { field: 'dailyCapacity', headerName: 'Daily Capacity', minWidth: 100, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header'},
      { field: 'grossCapacity', headerName: 'Gross Capacity', minWidth: 100, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header' },
      { field: 'timeOff', headerName: 'Time Off', minWidth: 100, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header' },
      { field: 'netCapacity', headerName: 'Net Capacity', minWidth: 100, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header' },
      {
        field: 'capacityUtilization',
        headerName: 'Capacity Utilization',
        minWidth: 100,
        flex: 1,
        cellClass:'cell-center',
        valueGetter: (params: any) => params.data.capacityUtilizationDetails.capacityUtilization ? params.data.capacityUtilizationDetails.capacityUtilization : '0',
        headerClass: 'wrap-header',
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.capacityUtilizationDetails.backgroundColor,
            color: params.data.capacityUtilizationDetails.fontColor,
            border: params.data.capacityUtilizationDetails.borderColor ? '1px solid '+params.data.capacityUtilizationDetails.borderColor+'':''
          };
        },          
      },
    ],
    ultilizationSummaryColdef:[
      { field: 'employeeType', headerName: 'Employee Type', minWidth: 130, flex: 1, wrapText:true,autoHeight: true},
      { field: 'numberOfEmployees', headerName: '# of Employees', minWidth: 130, flex: 1,cellClass:'cell-center'},
      { field: 'businessDaysMTD', headerName: 'Business Days MTD', minWidth: 150, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header'},
      { field: 'capacity', headerName: 'Capacity', minWidth: 130, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header', },
      { field: 'pto', headerName: 'PTO', minWidth: 120, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header', },
      { field: 'grossCapacity', headerName: 'Gross Capacity', minWidth: 100, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header' },
      { field: 'totalMTDHours', headerName: 'Total MTD Hours', minWidth: 100, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header'},
      { field: 'availableCapacity', headerName: 'Available Capacity', minWidth: 100, flex: 1,cellClass:'cell-center',headerClass: 'wrap-header' },
      {
        field: 'capacityUtilization',
        headerName: 'Capacity Utilization',
        minWidth: 100,
        flex: 1,
        cellClass:'cell-center',
        valueGetter: (params: any) => params.data.capacityUtilizationDetails.capacityUtilization ? params.data.capacityUtilizationDetails.capacityUtilization : '0',
        headerClass: 'wrap-header',
        cellStyle: (params:any) => {
          return {
            backgroundColor: params.data.capacityUtilizationDetails.backgroundColor,
            color: params.data.capacityUtilizationDetails.fontColor,
            border: params.data.capacityUtilizationDetails.borderColor ? '1px solid '+params.data.capacityUtilizationDetails.borderColor+'':''
          };
        },        
      },
    ],
    jiraTimeLogColDef:[
      {
        headerName: "Project",
        field: "team",
        width: 200,
        flex: 2,
        valueGetter: (params: any) => {
          if (params.data.isTotal) return params.data.team;
          if (params.data.isTeam) return params.data.team;
          if(params.data.isProject) return params.data.project;    
          if(params.data.isEpic) return params.data.epic;
          if(params.data.isTeamTotal) return params.data.team;          
        },
        colSpan: (params: any) => {
          if(params.data.isTotal) return 2;
          if(params.data.isTeam) return 4;
          if(params.data.isProject) return 4;
          if(params.data.isProject) return 4;
          if(params.data.isEpic) return 5; 
          if(params.data.isTeamTotal) return 2;
          return 1;
        },
        cellClass: (params: any) => {
          if(params.data.isTotal) return 'cell-left merged-total';
          if(params.data.isTeam) return 'cell-left merged-team';
          if(params.data.isProject) return 'cell-left merged-project';
          if(params.data.isEpic) return 'cell-left merged-epic';
          if(params.data.isTeamTotal) return 'cell-left merged-team-total';
          return '';
        }
      },      
      // {
      //   headerName: "Epic",
      //   field: "epic",
      //   width: 150,
      //   flex: 1,
      //   hide: false,
      //   cellClass: (params: any) => (params.data.isTotal || params.data.isTeam ? 'hidden-cell' : ''),
      // },
      {
        headerName: "Team Member",
        field: "user",
        width: 150,
        flex: 1,
        hide: false,
        cellClass: (params: any) => (params.data.isTotal || params.data.isTeam ? 'hidden-cell' : ''),
      },
      {
        headerName: "Hours",
        width: 200,
        flex: 2,
          valueGetter: (params: any) => {
          if (params.data.isTotal) return params.data.total; 
          if (params.data.isTeamTotal) return params.data.total;
          else return params.data.hours;          
        },
        headerClass: 'ag-center-header',
        hide: false,
        cellClass: (params: any) => {
          if(params.data.isTotal) return 'cell-center merged-total';     
          if(params.data.isTeamTotal) return 'cell-center merged-team-total';
          return 'cell-center';
        }        
      },     
    ],
    // -EOD
    applicationBugColDef:[
      { headerName: 'Project', field: 'project',flex:1,wrapText: true,autoHeight: true},
      { headerName: 'Key', field: 'issueKey',flex:1,cellRenderer: 'jiraIdRenderer',},
      { headerName: 'Summary', field: 'summary', wrapText:true,autoHeight: true,flex:2},
      { headerName: 'Assignee', field: 'assignee',flex:1 ,minWidth:130},
      { headerName: 'Development Stage', field: 'developmentStage',flex:1,minWidth:160, headerClass: 'wrap-header',wrapText: true,autoHeight: true,},
      { headerName: 'Requirements Due Date', field: 'requirementsDueDate',minWidth:160,flex:1, headerClass: 'wrap-header',cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.requirementsDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
       },
      { headerName: 'Analysis Due Date', field: 'analysisDueDate',minWidth:150,flex:1,headerClass: 'wrap-header',cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
      { headerName: 'Development Due Date', field: 'developmentDueDate',minWidth:160 ,flex:1,headerClass: 'wrap-header',cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
      { headerName: 'UAT Due Date', field: 'uatDueDate',minWidth:80,flex:1,headerClass: 'wrap-header',cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
       },
      { headerName: 'Production Due Date', field: 'productionDueDate',minWidth:150,flex:1,
        headerClass: 'wrap-header',
        cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
        cellStyle: (params:any) => {
          return {
            color: params?.data?.productionDueDateColor,
          };
        },
      }
    ],

    changeManagementColDef: [
      { headerName: 'Project', field: 'project', sortable: true, filter: true,wrapText: true,autoHeight: true },
      { headerName: 'Key', field: 'issueKey', sortable: true, filter: true,width:100,cellRenderer: 'jiraIdRenderer' },
      { headerName: 'Summary', field: 'summary',wrapText:true,autoHeight: true },
      { headerName: 'Assignee', field: 'assignee' },
      { headerName: 'Change Mgmt Approved', field: 'changeManagementApproved',width:170,headerClass: 'wrap-header',cellRenderer: 'dateInfoRenderer'  },
      { headerName: 'Development Stage', field: 'developmentStage',wrapText:true,autoHeight: true,headerClass: 'wrap-header',width:160},
      { headerName: 'Requirements Due Date', field: 'requirementsDueDate',headerClass: 'wrap-header',width:160 ,cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.requirementsDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
      { headerName: 'Analysis Due Date', field: 'analysisDueDate',headerClass: 'wrap-header',width:150 ,cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
       },
      { headerName: 'Development Due Date', field: 'developmentDueDate',headerClass: 'wrap-header',width:156,cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
       },
      { headerName: 'UAT Due Date', field: 'uatDueDate',headerClass: 'wrap-header',width:130 ,cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
      { headerName: 'Production Due Date', field: 'productionDueDate',headerClass: 'wrap-header',
        width:170,cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
        cellStyle: (params:any) => {
          return {
            color: params?.data?.productionDueDateColor,
          };
        },
       }
    ],
   salesforceDevelopPriorityColDef: [
    { headerName: 'Issue #', field: 'issueKey',flex:1,cellRenderer: 'jiraIdRenderer'  },
    { headerName: 'Description', field: 'summary', flex: 3,wrapText:true,autoHeight: true },
    { headerName: 'Owner', field: 'assignee',flex:1  },
    { headerName: 'Status', field: 'developmentStage',flex:1,autoHeight:true,wrapText:true  },
    { headerName: 'Req DD', field: 'requirementsDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
       valueGetter: (params: any) => params.data.requirementsDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'Analysis DD', field: 'analysisDueDate',flex:1 ,cellRenderer: 'dateInfoRenderer',
       valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'Dev DD', field: 'developmentDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
       valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
    { headerName: 'UAT DD', field: 'uatDueDate' ,flex:1,cellRenderer: 'dateInfoRenderer',
       valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'Prod DD', field: 'productionDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
       valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     }
    ],
    salesforceSupportPriorityColDef: [ 
    { headerName: 'Issue #', field: 'issueKey',flex:1,cellRenderer: 'jiraIdRenderer' },
    { headerName: 'Description', field: 'summary', flex: 3,wrapText:true,autoHeight: true },
    { headerName: 'Owner', field: 'assignee',flex:1,autoHeight:true,wrapText:true },
    { headerName: 'Status', field: 'developmentStage',flex:1,autoHeight:true,wrapText:true },
    { headerName: 'Req DD', field: 'requirementsDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.requirementsDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'Analysis DD', field: 'analysisDueDate',flex:1 ,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
    },
    { headerName: 'Dev DD', field: 'developmentDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'UAT DD', field: 'uatDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'Prod DD', field: 'productionDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     }
    ],
    oneButtonDevPrioritiesColdef:[   
    { headerName: 'Issue #', field: 'issueKey',flex:1,cellRenderer: 'jiraIdRenderer' },
    { headerName: 'Description', field: 'summary', flex: 3,wrapText:true,autoHeight: true },
    { headerName: 'Owner', field: 'assignee',flex:1 },
    { headerName: 'Status', field: 'developmentStage',flex:1,autoHeight:true,wrapText:true,
      cellStyle: (params:any) => {
          return {
            backgroundColor: params?.data?.colorCode?.developmentStage,
          };
        },
     },
    { headerName: 'Req DD', field: 'requirementsDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.requirementsDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      cellStyle: (params:any) => {
          return {
            backgroundColor: params?.data?.colorCode?.requirementsDueDate,
          };
        },
     },
    { headerName: 'Analysis DD', field: 'analysisDueDate',flex:1 ,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
      },
      cellStyle: (params:any) => {
          return {
            backgroundColor: params?.data?.colorCode?.analysisDueDate,
          };
        },
    },
    { headerName: 'Dev DD', field: 'developmentDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
      },
      cellStyle: (params:any) => {
          return {
            backgroundColor: params?.data?.colorCode?.developmentDueDate,
          };
        },
     },
    { headerName: 'UAT DD', field: 'uatDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
      },
      cellStyle: (params:any) => {
          return {
            backgroundColor: params?.data?.colorCode?.uatDueDate,
          };
        },
     },
    { headerName: 'Prod DD', field: 'productionDueDate',flex:1,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
      },
      cellStyle: (params:any) => {
          return {
            backgroundColor: params?.data?.colorCode?.productionDueDate,
          };
        },
     }
    ],
    oneDrawColDef:[
      { headerName: 'Key Open Issues Report', field: 'key', flex:1},
      { headerName: 'Total Count', field: 'count',flex:1 },
    ],
    baStatusColDef:[
    { headerName: 'Project', field: 'project', sortable: true, filter: true, flex:1,minWidth:120 },
    { headerName: 'Key',field: 'issueKey', sortable: true, filter: true, flex:1,cellRenderer: 'jiraIdRenderer' },
    { field: 'summary',headerName: 'Summary', sortable: true, filter: true, flex: 2,wrapText:true,autoHeight:true },
    { field: 'developmentStage', headerName: 'Development Stage', sortable: true, filter: true , flex:1,wrapText:true,autoHeight:true, headerClass: 'wrap-header', minWidth:155},
    { field: 'requirementsDueDate', headerName: 'Requirements Due Date', sortable: true, flex:1,headerClass: 'wrap-header', minWidth:155,cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.requirementsDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { field: 'analysisDueDate', headerName: 'Analysis Due Date', sortable: true, flex:1,headerClass: 'wrap-header', minWidth:150 ,cellRenderer: 'dateInfoRenderer',
      valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
    },
    { field: 'developmentDueDate', headerName: 'Development Due Date', sortable: true, flex:1,headerClass: 'wrap-header', minWidth:155 ,cellRenderer: 'dateInfoRenderer',
        valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { field: 'uatDueDate', headerName: 'UAT Due Date', sortable: true, flex:1,headerClass: 'wrap-header', minWidth:130,cellRenderer: 'dateInfoRenderer',
       valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
    },
    { field: 'productionDueDate', headerName: 'Production Due Date', sortable: true, flex:1,headerClass: 'wrap-header', minWidth:165,cellRenderer: 'dateInfoRenderer',
       valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
       cellStyle: (params:any) => {
          return {
            color: params.data.productionDueDateColor,
          };
        },
      },
    { field: 'baStatusUpdates', headerName: 'QA Status Update', sortable: true, filter: true, flex: 1 ,wrapText:true,headerClass: 'wrap-header',autoHeight:true,minWidth:150},
    ],
    changeManagementDashboardColDef:[
    { headerName: 'Project', field: 'project', sortable: true, filter: true, flex:1,minWidth:180 ,cellClass: 'cell-left',wrapText:true,autoHeight:true},
    { headerName: 'Key',field: 'issueKey', sortable: true, filter: true, flex:1,minWidth:100,cellClass: 'cell-left' },
    { headerName: 'Fresh Service Ticket',field: 'serviceTicket', sortable: true, filter: true, flex:1,minWidth:200,cellClass: 'cell-left',cellRenderer: 'freshServiceRenderer' },
    { headerName: 'Requester',field: 'requesterName', sortable: true, filter: true, flex:1,minWidth:200,cellClass: 'cell-left',wrapText:true,autoHeight:true },
    { headerName: 'Summary',  field: 'summary',sortable: true, filter: true, flex: 2,wrapText:true,autoHeight:true,minWidth:300,cellClass: 'cell-left'},
    { headerName: 'Development Stage',  field: 'developmentStage',sortable: true, filter: true, flex: 1,wrapText:true,autoHeight:true,minWidth:200,cellClass: 'cell-left' },
    { headerName: 'Change Management Status',field: 'changeManagementStatus', sortable: true, filter: true , flex:2,wrapText:true,autoHeight:true, minWidth:550,cellClass: 'cell-left'},
    { headerName: 'Requirement Due Date',field: 'requirementDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:210,cellClass: 'cell-left',
        valueGetter: (params: any) => params.data.requirementDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
    },
    { headerName: 'Analysis Due Date',field: 'analysisDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:190,cellClass: 'cell-left',
        valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'Development Due Date',field: 'developmentDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:210,cellClass: 'cell-left',
        valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
    { headerName: 'UAT Due Date',field: 'uatDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:160,cellClass: 'cell-left',
        valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
    { headerName: 'Production Due Date',field: 'productionDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:200,cellClass: 'cell-left',
        valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
    ],
    executiveDashboardColDef:[
    { headerName: 'Project', field: 'project', sortable: true, filter: true, flex:1,minWidth:180 ,cellClass: 'cell-left',wrapText:true,autoHeight:true},
    { headerName: 'Key',field: 'issueKey', sortable: true, filter: true, flex:1,cellRenderer: 'jiraIdRenderer',minWidth:100,cellClass: 'cell-left' },
    { headerName: 'Fresh Service Ticket',field: 'serviceTicket', sortable: true, filter: true, flex:1,minWidth:200,cellClass: 'cell-left',cellRenderer: 'freshServiceRenderer' },
    { headerName: 'Summary',  field: 'summary',sortable: true, filter: true, flex: 2,wrapText:true,autoHeight:true,minWidth:300,cellClass: 'cell-left'},
    { headerName: 'Development Stage',  field: 'developmentStage',sortable: true, filter: true, flex: 1,wrapText:true,autoHeight:true,minWidth:200,cellClass: 'cell-left' },
    { headerName: 'Requirement Due Date',field: 'requirementDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:210,cellClass: 'cell-left',
      valueGetter: (params: any) => params.data.requirementDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
    },
    { headerName: 'Analysis Due Date',field: 'analysisDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:190,cellClass: 'cell-left',
      valueGetter: (params: any) => params.data.analysisDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    { headerName: 'Development Due Date',field: 'developmentDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:210,cellClass: 'cell-left',
      valueGetter: (params: any) => params.data.developmentDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
    { headerName: 'UAT Due Date',field: 'uatDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:160,cellClass: 'cell-left',
      valueGetter: (params: any) => params.data.uatDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
      },
    { headerName: 'Production Due Date',field: 'productionDueDate', sortable: true, flex:1,cellRenderer: 'dateInfoRenderer',minWidth:200,cellClass: 'cell-left', 
      valueGetter: (params: any) => params.data.productionDueDate,
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: getDateComparator(),
          browserDatePicker: true
        },
     },
    ],
    
  };
// Date Filter 
function getDateComparator() {
  return (filterLocalDateAtMidnight: Date, cellValue: string) => {
        if (!cellValue) return -1;
        const cellDate = new Date(cellValue);
        const cellDateNoTime = new Date(
          cellDate.getFullYear(),
          cellDate.getMonth(),
          cellDate.getDate()
        );
        if (cellDateNoTime < filterLocalDateAtMidnight) return -1;
        if (cellDateNoTime > filterLocalDateAtMidnight) return 1;
        return 0;
  };
}
  