import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UltilizationDashboardComponent } from './ultilization-dashboard.component';

describe('UltilizationDashboardComponent', () => {
  let component: UltilizationDashboardComponent;
  let fixture: ComponentFixture<UltilizationDashboardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UltilizationDashboardComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(UltilizationDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
