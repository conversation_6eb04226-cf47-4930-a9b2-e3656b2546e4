import { Component, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { LoaderService } from 'loader';
import { IDropdownSettings, MultiSelectComponent } from 'ng-multiselect-dropdown';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { environment } from 'src/environments/environment';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import moment from 'moment';
import { Subscription } from 'rxjs';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { SharedService } from 'src/app/service/shared.service';
@Component({
    selector: 'app-daily-dashboard-filter',
    templateUrl: './daily-dashboard-filter.component.html',
    styleUrls: ['./daily-dashboard-filter.component.scss'],
    standalone: false
})
/* 
  Daily Dashboard Filter Component
*/
export class DailyDashboardFilterComponent implements OnInit {
  projectFilterForm!: FormGroup;
  assetURL = environment.appBaseURL;
  projectList: any[] = [];
  teamList: any[] = [];
  memberList: any[] = [];
  columnDefs: any[] = dashboardConstants.dailydashboardColDef;
  exportData: any[] = [];
  projectData: any[] = [];
  memberData: any[] = [];
  initialArray: any[] = [{
    key: 'All', value: 'All'
  }]
  @Input() masterData: any;
  @Input() excelData: any;
  @Output() emitFilters = new EventEmitter<any>();
  @ViewChildren('project,teams,members') dropdowns!: QueryList<MultiSelectComponent>;
  private masterDataSubscription!: Subscription;

  constructor(private onePmService: OnepmServiceService, private loaderService: LoaderService, private notification: NzNotificationService, private formBuilder: UntypedFormBuilder, private sharedService: SharedService) { }
  
  ngOnInit(): void {
    this.initializeForm();
    this.getMasterData();
  }
  ngOnChanges() {
    this.exportData = this.excelData;
  }
  /**
   * Method to return the ng-multiselect dropdown settings
   * @param singleSelection 
   * @param enableCheckAll 
   * @param allowSearchFilter 
   * @param id 
   * @param name 
   * @returns 
   */
  filterSettings(singleSelection: boolean, enableCheckAll: boolean, allowSearchFilter: boolean, id: any = 'id', name: any = 'name'): IDropdownSettings {
    return ({
      singleSelection: singleSelection,
      idField: id,
      textField: name,
      selectAllText: 'All',
      unSelectAllText: 'All',
      itemsShowLimit: 1,
      enableCheckAll: enableCheckAll,
      allowSearchFilter: allowSearchFilter
    });
  }

  /**
   * Methhod to initialize the projectFilterForm
   * @returns 
   */
  initializeForm(): FormGroup {
    return this.projectFilterForm = this.formBuilder.group({
      project: [null],
      teams: [null],
      members: [null],
      worklogDate: [new Date(), Validators.required],
    });
  }
  /**
     * Function to close ng select dropdown  while opening date picker
     */
  closeAllDropdowns(): void {
    this.dropdowns.forEach((dropdown) => {
      dropdown.closeDropdown();
    });
  }
  onOpenChange(isOpen: boolean): void {
    if (isOpen) this.closeAllDropdowns();
  }
  /**
 * Function to get master data from api
 */
  getMasterData() {
    this.loaderService.invokeLoaderComponent(true);
    this.masterDataSubscription = this.onePmService.getMasterData().subscribe((res: any) => {
      if (res.succeeded) {
        this.loaderService.invokeLoaderComponent(false);
        this.masterData = res.data;
        this.sharedService.legends = this.masterData?.legendsInfo;
        this.memberData = res.data.members;
        this.projectData = this.initialArray.concat(res.data.projects);
        this.projectFilterForm.controls['project'].setValue([this.projectData[0]]);
        this.onSearch(false)
      }
      else {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
  /**
   * Method to emit the filter values to the parent component
   * @param clear 
   */
  onSearch(clear?: boolean) {
    if (!this.projectFilterForm.valid) {
      this.projectFilterForm.markAllAsTouched();
      this.notification.blank('', 'Please select Work Log Date', { nzPlacement: 'bottomLeft', nzClass: 'error' });
      return;
    }
    const project = this.projectFilterForm.controls['project'].value?.map((item: any) => item['key']).join(',');
    const team = this.projectFilterForm.controls['teams'].value?.map((item: any) => item['value']).join(',');
    const member = this.projectFilterForm.controls['members'].value?.map((item: any) => item['userId']).join(',');
    const workLogDate = this.changeDateFormats(this.projectFilterForm.controls['worklogDate'].value);
    const payloadData = {
      project: project,
      team: team,
      member: member,
      workLogDate: workLogDate
    }
    this.emitFilters.emit({ data: payloadData, clearFilter: clear });
  }
  /**
   * Method to export the grid to excel
   */
  onExportDaily() {
    this.loaderService.invokeLoaderComponent(true);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Daily Dashboard');
    const title = 'Daily_Dashboard_List' + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
    let headers: any = [];
    this.columnDefs.forEach(ele => {
      if (ele.headerName != '') headers.push(ele.headerName);
    });
    // Add Header Row
    worksheet.addRow(headers);
    // Style Header Row
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    });
    // Add Data Rows
    this.exportData.forEach((data) => {
      const row = worksheet.addRow([
        data.project,
        data.epic,
        data.description,
        this.convertHtmlToBullets(data.workDescriptions),
        data.status,
        data.workLogInfo.hours,
        data.remaining,
        `${data.hoursCompleted}%`,
      ]);
      // Style the Due Date Column (Column 5)
      const dueDateCell = row.getCell(6);
      this.onHighlightCell(dueDateCell, data.workLogInfo);
    });
    // Adjust Column Widths
    worksheet.columns.forEach((column) => {
      worksheet.getColumn(3).width = 50;
      worksheet.getColumn(4).width = 50;
      worksheet.getColumn(4).alignment = { wrapText: true };
      worksheet.getColumn(8).alignment = { horizontal: 'right' };
      column.width = 20; // Adjust based on content
    });
    // Add Legend Rows
    worksheet.addRow([]);
    worksheet.addRow(['Legends']).font = { bold: true, size: 14 };
    worksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
    const legends = this.sharedService.legends;
    legends.forEach((legend: any, index: number) => {
      const row = worksheet.addRow([legend.category]);
      // Set Cell Styles
      const cell = row.getCell(1);
      this.onHighlightCell(cell, legend);
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
      // Set Row Height
      worksheet.getRow(row.number).height = 25;
    });
    // Export Workbook
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, title + '.xlsx');
    });
    this.loaderService.invokeLoaderComponent(false);
  }
  /**
   * Method to format cell style based on the data
   * @param cell 
   * @param data 
   */
  onHighlightCell(cell: any, data: any) {
    if (data.backgroundColor) {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: data.backgroundColor.replace('#', '') },
      };
    }
    if (data.fontColor) {
      cell.font = { color: { argb: data.fontColor.replace('#', '') } };
    }
    if (data.borderColor) {
      cell.border = {
        top: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        left: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        bottom: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        right: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
      };
    }
  }
  /**
   * Method to convert html string to bullets
   * @param html 
   * @returns 
   */
  convertHtmlToBullets(html: string): string {
    let result = html.replace(/<\/?ul>/g, '\r\n');
    result = result.replace(/<li>/g, '• ').replace(/<\/li>/g, '\r\n');
    return result.trim();
  }
  /**
   * Method to change date format 'YYYY-MM-DD'
   * @param value 
   * @returns 
   */
  changeDateFormats(value: Date) {
    return moment(value).format('YYYY-MM-DD')
  }

  /**
   * Method to assign value to form control while project dropdown change
   * @param event 
   */
  onProjectSelect(event: any) {
    if (event.key == 'All')
      this.projectFilterForm.controls['project'].setValue([{ key: 'All', value: 'All' }])
    else
      this.projectFilterForm.controls['project'].setValue(this.projectFilterForm.controls['project'].value.filter((s: any) => s.key != 'All'))
  }
/**
 * Method reset form clicking on clear filter button
 */
  onClearFilter() {
    this.projectFilterForm.reset();
    this.projectFilterForm.controls['project'].setValue([this.projectData[0]]);
    this.projectFilterForm.controls['worklogDate'].setValue(new Date());
    this.onSearch(true);
  }
  /**
   * Method to filter members list according teams dropdown list
   */
  onTeamSelect() {
    const selectedValues = this.projectFilterForm.get('teams')?.value;
    const selectedMemberValues = this.projectFilterForm.get('members')?.value;
    if (selectedValues) {
      this.memberData = this.masterData?.members.filter((val: any) => selectedValues.map((s: any) => s.key).includes(val.groupId));
    }
    if (selectedValues.length == 0) {
      this.memberData = this.masterData?.members;
    }
    const isKeyIncluded = selectedMemberValues?.some((value: any) => this.memberData.some((val: any) => val.userId === value.userId));
    if (!isKeyIncluded) this.projectFilterForm.controls['members'].setValue([]);
  }
  /**
   * Method to unsubscribe subscription while existing component
   */
  ngOnDestroy(): void {
    if (this.masterDataSubscription) {
      this.masterDataSubscription.unsubscribe();
    }
  }
}
