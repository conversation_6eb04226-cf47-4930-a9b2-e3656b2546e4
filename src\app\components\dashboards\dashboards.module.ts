import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { AgGridModule } from 'ag-grid-angular';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

import { DashboardsRoutingModule } from './dashboards-routing.module';
import { DashboardComponent } from './dashboard.component';
import { ProjectDashboardComponent } from './project-dashboard/project-dashboard.component';
import { ProjectDashboardFilterComponent } from './project-dashboard/project-dashboard-filter/project-dashboard-filter.component';
import { ProjectDashboardGridComponent } from './project-dashboard/project-dashboard-grid/project-dashboard-grid.component';
import { DailyDashboardComponent } from './daily-dashboard/daily-dashboard.component';
import { DailyDashboardFilterComponent } from './daily-dashboard/daily-dashboard-filter/daily-dashboard-filter.component';
import { DailyDashboardGridComponent } from './daily-dashboard/daily-dashboard-grid/daily-dashboard-grid.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UnauthorizedComponent } from '../shared-components/unauthorized/unauthorized.component';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { PmoDashboardComponent } from './pmo-dashboard/pmo-dashboard.component';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { PastDueDateGridComponent } from './pmo-dashboard/past-due-date-grid/past-due-date-grid.component';
import { MissingDueDateGridComponent } from './pmo-dashboard/missing-due-date-grid/missing-due-date-grid.component';
import { MissingEstimateDateGridComponent } from './pmo-dashboard/missing-estimate-date-grid/missing-estimate-date-grid.component';
import { AtRiskGridComponent } from './pmo-dashboard/at-risk-grid/at-risk-grid.component';
import { KpiDashboardComponent } from './kpi-dashboard/kpi-dashboard.component';
import { KpiFilterComponent } from './kpi-dashboard/kpi-filter/kpi-filter.component';
import { HoursAndTicketsGridComponent } from './kpi-dashboard/hours-and-tickets-grid/hours-and-tickets-grid.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { HoursAnalysisByTeamComponent } from './kpi-dashboard/hours-analysis-by-team/hours-analysis-by-team.component';
import { KpiKeyFeaturesComponent } from './kpi-dashboard/kpi-key-features/kpi-key-features.component';
import { KpiDeploymentSuccessComponent } from './kpi-dashboard/kpi-deployment-success/kpi-deployment-success.component';
import { UltilizationDashboardComponent } from './ultilization-dashboard/ultilization-dashboard.component';
import { UtilizationGridComponent } from './ultilization-dashboard/utilization-grid/utilization-grid.component';
import { UtilizationFilterComponent } from './ultilization-dashboard/utilization-filter/utilization-filter.component';
import { TeamsDashboardComponent } from './teams-dashboard/teams-dashboard.component';
import { AddMemberComponent } from './teams-dashboard/modal/add-member/add-member.component';
import { AddProjectComponent } from './teams-dashboard/modal/add-project/add-project.component';
import { AddTeamComponent } from './teams-dashboard/modal/add-team/add-team.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { StatusDashboardComponent } from './status-dashboard/status-dashboard.component';




@NgModule({
  declarations: [
    DashboardComponent,
    ProjectDashboardComponent,
    ProjectDashboardFilterComponent,
    ProjectDashboardGridComponent,
    DailyDashboardComponent,
    DailyDashboardFilterComponent,
    DailyDashboardGridComponent,
    PmoDashboardComponent,
    PastDueDateGridComponent,
    MissingDueDateGridComponent,
    MissingEstimateDateGridComponent,
    AtRiskGridComponent,
    KpiDashboardComponent,
    KpiFilterComponent,
    HoursAndTicketsGridComponent,
    HoursAnalysisByTeamComponent,
    KpiKeyFeaturesComponent,
    KpiDeploymentSuccessComponent,
    UltilizationDashboardComponent,
    UtilizationGridComponent,
    UtilizationFilterComponent,
    TeamsDashboardComponent,
    AddMemberComponent,
    AddProjectComponent,
    AddTeamComponent,
    StatusDashboardComponent,
  ],
  imports: [
    CommonModule,
    DashboardsRoutingModule,
    NgMultiSelectDropDownModule.forRoot(),
    AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    NzButtonModule,
    NzDatePickerModule,
    NzCollapseModule,
    UnauthorizedComponent,
    NzSpinModule,
    HighchartsChartModule,
    DragDropModule,
    NzModalModule,
  ],
  providers: [
    NzNotificationService
  ]
})
export class DashboardsModule { }
