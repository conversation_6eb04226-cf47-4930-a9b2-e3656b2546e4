import { Component, OnInit } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
    selector: 'app-hour',
    templateUrl: './hour.component.html',
    styleUrls: ['./hour.component.scss'],
    standalone: false
})
export class HourComponent  implements AgRendererComponent {

  cellValue:any;
  agInit(params: ICellRendererParams): void { 
    this.cellValue = params.value;
   }
  refresh(params: ICellRendererParams):boolean {
    this.cellValue =params;
    return true
  }
  

}
