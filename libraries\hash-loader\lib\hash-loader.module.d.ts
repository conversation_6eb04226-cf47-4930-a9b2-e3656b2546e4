import * as i0 from "@angular/core";
import * as i1 from "./hash-loader.component";
import * as i2 from "@angular/platform-browser";
export declare class HashLoaderModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<HashLoaderModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<HashLoaderModule, [typeof i1.HashLoaderComponent], [typeof i2.BrowserModule], [typeof i1.HashLoaderComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<HashLoaderModule>;
}
