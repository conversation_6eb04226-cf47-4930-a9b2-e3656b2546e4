<div class="m-4 pt-3">
    <!-- Filter Section remains unchanged -->
    <div class="tag-container">
        <ng-container>
            <div class="tag white"
                [ngClass]="(selectedTeam === '' && selectedProjectLead === '') ? 'tag white selected' : 'tag white'"
                (click)="resetFilter()">
                <span>All</span>
                <span class="badge">{{teams.length}}</span>
            </div>
        </ng-container>
        <div class="tag teal" (click)="showAllTeams = !showAllTeams">
            <span>All Teams</span>
            <span class="badge">{{ uniqueTeams.length }}</span>
            <span class="icon">
                <i class="fa" [ngClass]="{'fa-chevron-right': !showAllTeams, 'fa-chevron-left': showAllTeams}"></i>
            </span>
        </div>
        <ng-container *ngIf="showAllTeams">
            <ng-container *ngFor="let team of uniqueTeams; let uniqueTeamsIndex = index">
                <div class="tag teal" [ngClass]="(selectedTeam === team) ? 'tag teal selected' : 'tag teal'"
                    (click)="filterByTeam(team)">
                    <span>{{team}}</span>
                    <span class="badge">{{ getTeamCount(team) }}</span>
                </div>
            </ng-container>
        </ng-container>
        <div class="tag blue" (click)="showAllManagers = !showAllManagers">
            <span>All Managers</span>
            <span class="badge">{{ uniqueProjectLeads.length }}</span>
            <span class="icon">
                <i class="fa"
                    [ngClass]="{'fa-chevron-right': !showAllManagers, 'fa-chevron-left': showAllManagers}"></i>
            </span>
        </div>
        <ng-container *ngIf="showAllManagers">
            <ng-container *ngFor="let projectLead of uniqueProjectLeads; let uniqueProjectLeadsIndex = index">
                <div class="tag blue"
                    [ngClass]="(selectedProjectLead === projectLead) ? 'tag blue selected' : 'tag blue'"
                    (click)="filterByProjectLead(projectLead)">
                    <span>{{projectLead}}</span>
                    <span class="badge">{{ getProjectLeadCount(projectLead) }}</span>
                </div>
            </ng-container>
        </ng-container>
        <button type="button" title="Add Team" (click)="addTeam()"
            class="btn btn-sm btn-success add-job-btn ms-auto m-1">
            <i class="fa fa-plus ico-align"></i>
        </button>
    </div>
 
    <div class="row">
        <div class="col-xl-4 col-lg-6 p-1" *ngFor="let team of filteredTeams; let teamIndex = index">
            <div class="card p-0">
                <div class="customize-header">
                    <div class="card-title mb-0 team-style-section d-flex justify-content-between">
                        <div class="ms-1 team-heading-label">{{team.teamName}}</div>
                        <div class="team-type-icon">
                            <div class="tag blue"  title="{{team.teamDescription}}">
                                <span class="text-wrap">{{team.teamDescription}}</span>
                            </div>
                        </div>
                        <div >
                            <button type="button" title="Add Team Member" (click)="addTeamMember(team)"
                            class="btn btn-sm btn-success add-job-btn ms-auto">
                            <i class="fa fa-plus ico-align"></i>
                        </button>
                        <button type="button" title="Delete Team" (click)="deleteTeam(team.teamId)"
                            class="btn btn-sm btn-danger add-job-btn mx-1">
                            <i class="fa fa-trash ico-align"></i>
                        </button>
                        </div>                        
                    </div>
                </div>
                <div class="card-body team-details">
                    <div class="accordions" [id]="'accordionInternalTeams' + teamIndex">
                        <div class="accordion-items">
                            <div class="internal-teams-section">
                                <div class="display-flex">
                                    <p class="title m-0 ms-2 internal-teams-cursor" data-bs-toggle="collapse"
                                        (click)="toggleAccordion(teamIndex)"
                                        [attr.data-bs-target]="'#collapseInternalTeams' + getSanitizedId(team.teamName, teamIndex)"
                                        aria-expanded="true"
                                        [attr.aria-controls]="'collapseInternalTeams' + getSanitizedId(team.teamName, teamIndex)">
                                        Teams <i class="fa"
                                            [ngClass]="accordionState[teamIndex] ? 'fa fa-chevron-down' : 'fa fa-chevron-up'"></i>
                                    </p>                                    
                                </div>
                                <div [id]="'collapseInternalTeams' + getSanitizedId(team.teamName, teamIndex)"
                                    class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        <div class="accordions" id="accordionSubTeams">
                                            <div class="accordion-items"
                                                *ngFor="let type of team.categoryAndMembers; let typeIndex = index">
                                                <div class="member-section m-1">
                                                    <div class="display-flex justify-content-between">
                                                        <p class="title m-0 ms-2 member-cursor"
                                                            data-bs-toggle="collapse"
                                                            (click)="toggleInnerAccordion(teamIndex,type.category)"
                                                            [attr.data-bs-target]="'#collapse' + getSanitizedId(team.teamName, type.category + typeIndex)"
                                                            aria-expanded="false"
                                                            [attr.aria-controls]="'collapse' + getSanitizedId(team.teamName, type.category + typeIndex)">
                                                            {{ type.category }} <i class="fa"
                                                                [ngClass]="innerAccordionState[teamIndex]?.[type.category] ? 'fa fa-chevron-up' : 'fa fa-chevron-down'"></i>
                                                        </p>
                                                       
                                                    </div>
                                                    <div [id]="'collapse' + getSanitizedId(team.teamName, type.category + typeIndex)"
                                                        class="accordion-collapse collapse">
                                                        <div class="accordion-body">
                                                            <div class="member-drop-section m-2 mb-0">
                                                                <div *ngFor="let member of type.members">
                                                                    <ul class="list-group">
                                                                        <li class="list-group-item">
                                                                            <span class="job-type-title text-wrap">{{
                                                                                member.name |
                                                                                titlecase }}
                                                                                ({{member.groupName}})</span>
                                                                            <button class="delete-btn mt-1" (click)="deleteTeamMember(member,team?.teamId)"
                                                                                title="Delete Employee">
                                                                                <i
                                                                                    class="fa fa-solid fa-trash delete-btn text-danger"></i>
                                                                            </button>
                                                                            <span class="team-member-role me-2 mt-1">{{
                                                                                member.skill }}</span>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                                <div *ngIf="type.members?.length === 0"
                                                                    class="empty-list-placeholder">
                                                                    No employees available
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div *ngIf="team.categoryAndMembers.length == 0" class="text-center">
                                                <span class="font-size-custom">No category found</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Projects section remains unchanged -->
                    <div class="accordions" [id]="'accordionProjects' + teamIndex">
                        <div class="accordion-items">
                            <div class="project-section mt-2">
                                <div class="display-flex">
                                    <p class="title m-0 ms-2 project-cursor" data-bs-toggle="collapse"
                                        [attr.data-bs-target]="'#collapseProjects' + getSanitizedId(team.teamName)"
                                        aria-expanded="true"
                                        [attr.aria-controls]="'collapseProjects' + getSanitizedId(team.teamName)">
                                        Projects
                                    </p>
                                    <button type="button" title="Add Project" (click)="addProject(team)"
                                        class="btn btn-sm btn-success add-job-btn ms-auto m-1">
                                        <i class="fa fa-plus ico-align"></i>
                                    </button>
                                    <button type="button" title="Save Project" *ngIf="isProjectModified(teamIndex)" (click)="saveUpdatedProject(team)"
                                        class="btn btn-sm save-btn-project add-job-btn m-1">
                                        <i class="fa fa-save ico-align text-light"></i>
                                    </button>
                                </div>
 
                                <div [id]="'collapseProjects' + getSanitizedId(team.teamName)"
                                    class="accordion-collapse collapse show project-custom-padding">
                                    <div class="accordion-body">
                                        <div class="project-drop-section m-1" #projectDropSection>
                                            <table class="table table-responsive mb-2">
                                                <thead>
                                                    <tr>
                                                        <th>Project</th>
                                                        <th>Priority</th>
                                                        <th></th>
                                                    </tr>
                                                </thead>
                                                <tbody cdkDropList [id]="team.teamName+'-'+teamIndex"
                                                    [cdkDropListData]="team.projects" class="list"
                                                    (cdkDropListDropped)="drop($event,team.projects,teamIndex)">
                                                    <tr *ngFor="let project of team.projects; let i = index" cdkDrag
                                                        style="cursor: move;">
                                                        <td>{{ project.projectName }}</td>
                                                        <td>{{ project.priority }}</td>
                                                        <td>
                                                            <button class="delete-btn" title="Delete Project" (click)="deleteProject(project.id , team.teamId)">
                                                                <i
                                                                    class="fa fa-solid fa-trash delete-btn text-danger"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div *ngIf="team.projects.length === 0" class="empty-list-placeholder font-size-custom">
                                                No projects found
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
 
                </div>
            </div>
        </div>
    </div>
</div>