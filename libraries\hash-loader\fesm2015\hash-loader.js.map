{"version": 3, "file": "hash-loader.js", "sources": ["../../../projects/hash-loader/src/lib/loader.service.ts", "../../../projects/hash-loader/src/lib/hash-loader.component.ts", "../../../projects/hash-loader/src/lib/hash-loader.component.html", "../../../projects/hash-loader/src/lib/hash-loader.module.ts", "../../../projects/hash-loader/src/public-api.ts", "../../../projects/hash-loader/src/hash-loader.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class LoaderService {\n  isLoading = new Subject<boolean>();\n\n  invokeLoaderComponent(status:boolean){\n    this.isLoading.next(status);\n  }\n}\n", "import { Component, Input, OnInit } from '@angular/core';\nimport { LoaderService } from './loader.service';\n\n@Component({\n  selector: 'lib-hash-loader',\n  templateUrl: './hash-loader.component.html',\n  styleUrls: ['./hash-loader.component.scss']\n})\nexport class HashLoaderComponent implements OnInit {\n\n  isLoading:boolean = false;\n  @Input() topOffSet:any;\n\n  heightStyle :any;\n\n  constructor(private loaderService: LoaderService) { }\n\n  ngOnInit(): void {\n    if(this.topOffSet) this.heightStyle = `height: calc(100vh - ${this.topOffSet})`;\n    else this.heightStyle = 'height: 100vh';\n    this.loaderService.isLoading.subscribe((status:boolean) => {\n      this.isLoading = status;\n    });\n  }\n\n}\n", "<div *ngIf=\"isLoading && heightStyle\" id=\"pause\" class=\"d-flex align-items-center justify-content-center\" [attr.style]=\"heightStyle\">\r\n      <div class=\"hash-loader\"></div>\r\n</div>\r\n", "import { NgModule } from '@angular/core';\nimport { HashLoaderComponent } from './hash-loader.component';\nimport { BrowserModule } from '@angular/platform-browser';\n\n\n\n@NgModule({\n  declarations: [\n    HashLoaderComponent\n  ],\n  imports: [\n    BrowserModule\n  ],\n  exports: [\n    HashLoaderComponent\n  ]\n})\nexport class HashLoaderModule { }\n", "/*\n * Public API Surface of hash-loader\n */\n\nexport * from './lib/loader.service';\nexport * from './lib/hash-loader.component';\nexport * from './lib/hash-loader.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["i1.LoaderService"], "mappings": ";;;;;;MAMa,aAAa,CAAA;AAH1B,IAAA,WAAA,GAAA;AAIE,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,OAAO,EAAW,CAAC;AAKpC,KAAA;AAHC,IAAA,qBAAqB,CAAC,MAAc,EAAA;AAClC,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC7B;;0GALU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAb,aAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cAFZ,MAAM,EAAA,CAAA,CAAA;2FAEP,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;AACnB,iBAAA,CAAA;;;MCGY,mBAAmB,CAAA;AAO9B,IAAA,WAAA,CAAoB,aAA4B,EAAA;QAA5B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QALhD,IAAS,CAAA,SAAA,GAAW,KAAK,CAAC;KAK2B;IAErD,QAAQ,GAAA;QACN,IAAG,IAAI,CAAC,SAAS;YAAE,IAAI,CAAC,WAAW,GAAG,CAAA,qBAAA,EAAwB,IAAI,CAAC,SAAS,GAAG,CAAC;;AAC3E,YAAA,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAc,KAAI;AACxD,YAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;AAC1B,SAAC,CAAC,CAAC;KACJ;;gHAfU,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAnB,mBAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,2FCRhC,wMAGA,EAAA,MAAA,EAAA,CAAA,gxCAAA,CAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;2FDKa,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAL/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,WAAW,EAAE,8BAA8B;oBAC3C,SAAS,EAAE,CAAC,8BAA8B,CAAC;AAC5C,iBAAA,CAAA;iGAIU,SAAS,EAAA,CAAA;sBAAjB,KAAK;;;MEMK,gBAAgB,CAAA;;6GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAhB,gBAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EATzB,YAAA,EAAA,CAAA,mBAAmB,CAGnB,EAAA,OAAA,EAAA,CAAA,aAAa,aAGb,mBAAmB,CAAA,EAAA,CAAA,CAAA;AAGV,gBAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EAPlB,OAAA,EAAA,CAAA;YACP,aAAa;AACd,SAAA,CAAA,EAAA,CAAA,CAAA;2FAKU,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAX5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,YAAY,EAAE;wBACZ,mBAAmB;AACpB,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,aAAa;AACd,qBAAA;AACD,oBAAA,OAAO,EAAE;wBACP,mBAAmB;AACpB,qBAAA;AACF,iBAAA,CAAA;;;AChBD;;AAEG;;ACFH;;AAEG;;;;"}