import { Component } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import moment from 'moment';

@Component({
    selector: 'app-date-info',
    templateUrl: './date-info.component.html',
    styleUrls: ['./date-info.component.scss'],
    standalone: false
})
export class DateInfoComponent implements AgRendererComponent {
  cellValue: any;
  params: any;

  agInit(params: ICellRendererParams): void {
    this.params = params;
    this.cellValue = this.getValueToDisplay(params);
  }
  refresh(params: ICellRendererParams): boolean {
    this.cellValue = this.getValueToDisplay(params);
    return true;
  }
  /**
   * Get Value To Display
   * Format into the MM-DD-YYYY format if the field have value
   * @param params
   * @returns
   */
  getValueToDisplay(params: ICellRendererParams) {
    let rawDate = '';

    if (!params.value || params.value === '') {
      return '';
    }
    // Check if value is an object with a 'date' property
    if (typeof params.value === 'object' && params.value.date) {
      rawDate = params.value.date;
    } else if (typeof params.value === 'string') {
      rawDate = params.value;
    }
    if (rawDate === '0001-01-01T00:00:00') {
      return 'TBD';
    }
    const formattedDate = moment(rawDate).isValid()
      ? moment(rawDate).format('MM-DD-YYYY')
      : '';
    return formattedDate;
  }
}
