import { Component, Input, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment';
 
@Component({
    selector: 'app-project-dashboard-grid',
    templateUrl: './project-dashboard-grid.component.html',
    styleUrls: ['./project-dashboard-grid.component.scss'],
    standalone: false
})
export class ProjectDashboardGridComponent implements OnInit {
  rowData: any[] = [];
  @Input() gridData: any;
  @Input() projectFlag: any;
  jiraUrl = '';
  assetURL = environment.appBaseURL;
  expandedKeys: Set<string> = new Set();
 
  constructor() { }
 /**
   * Lifecycle hook that is called after data-bound properties are initialized
  */
  ngOnInit(): void {
    this.rowData = this.gridData;
    this.jiraUrl = environment.jiraUrl;
    this.expandAllParents();
  }
  /**
   * Lifecycle hook that is called after data-bound properties are initialized
  */
  ngOnChanges() {
    this.rowData = this.gridData;
    this.expandAllParents();
 
  }
  /**
   * function to call default table expand option
  */
  expandAllParents(): void {
    const parents = this.rowData
      .filter(row => this.getChildren(row.issueKey).length > 0)
      .map(row => row.issueKey);
 
    this.expandedKeys = new Set(parents);
  }
 /**
   * Click event of key td for expand and collapse
   * @param issueKey field of the table
  */
  toggleRow(issueKey: string): void {
    this.expandedKeys.has(issueKey)
      ? this.expandedKeys.delete(issueKey)
      : this.expandedKeys.add(issueKey);
  }
/**
   * Click event of key td for expand and collapse
   * @param issueKey field of the table
  */
  isExpanded(issueKey: string): boolean {
    return this.expandedKeys.has(issueKey);
  }
  /**
   * filter matching master keys
   * @param masterKey parent field
  */
  getChildren(masterKey: string): any[] {
    return this.rowData.filter(row => row.masterKey === masterKey);
  }
  /**
   * For getting top layer like Epic
  */
  getTopLevelRows(): any[] {
    const allKeys = new Set(this.rowData.map(r => r.issueKey));
    return this.rowData.filter(row => !allKeys.has(row.masterKey));
    // return this.rowData.filter(row => row.masterKey === '');
  }
  /**
   * Calculating Total Hours
   * @param issueKey field of the table
  */
  getTotalHours(issueKey: string): number {
    const children = this.getChildren(issueKey);
 
    if (children.length === 0) {
      // Leaf node — return its own hours
      const row = this.rowData.find(r => r.issueKey === issueKey);
      return row?.workLogInfo?.hours ?? 0;
    }
 
    // Parent — sum up children's hours
    let total = 0;
    for (const child of children) {
      total += this.getTotalHours(child.issueKey);
    }
    return total;
  }
  /**
   * Calculating Remaining Hours
   * @param issueKey field of the table
   *
  */
  getTotalRemainingHours(issueKey: string): number {
    const children = this.getChildren(issueKey);
 
    if (children.length === 0) {
      // Leaf node — return its own remaining hours
      const row = this.rowData.find(r => r.issueKey === issueKey);
      return row?.remainingHours ?? 0;
    }
 
    // Parent — sum up children's remaining hours
    let total = 0;
    for (const child of children) {
      total += this.getTotalRemainingHours(child.issueKey);
    }
    return total;
  }
  /**
   * Calculating Percentage Calculate Hours
   * @param issueKey field of the table
   *
  */
  getPercentageHoursComplete(issueKey: string): string {
    const totalHours = this.getTotalHours(issueKey);
    const totalRemaining = this.getTotalRemainingHours(issueKey);
 
    const denominator = totalRemaining + totalHours;
 
    if (denominator === 0) {
      return '0%'; // Avoid division by zero
    }
 
    const percentage = (totalRemaining / denominator) * 100;
 
    // return `${percentage.toFixed(1)}%`;
    return Number.isInteger(percentage)? `${percentage}%`: `${percentage.toFixed(1)}%`;
  }
  /**
   * Calculating Status Calculate Hours
   * @param issueKey field of the table
   *
  */
  getEpicStatus(epicKey: string): string {
    const children = this.getChildren(epicKey);
    const allIssues: any[] = [];
 
    children.forEach(child => {
      allIssues.push(child);
      const grandChildren = this.getChildren(child.issueKey);
      allIssues.push(...grandChildren);
    });
 
    if (allIssues.length === 0) return 'In Progress';
 
    const allDone = allIssues.every(issue => issue.developmentStage === 'Done - Close');
    return allDone ? 'Done' : 'In Progress';
  }
 
 
}