import {Component,ElementRef,OnInit,QueryList,ViewChildren,} from '@angular/core';
import { NzModalService, NzModalRef } from 'ng-zorro-antd/modal';
import { Team } from 'src/app/utils/apps.interface';
import { AddMemberComponent } from './modal/add-member/add-member.component';
import { AddTeamComponent } from './modal/add-team/add-team.component';
import { AddProjectComponent } from './modal/add-project/add-project.component';
import {CdkDragDrop,moveItemInArray,transferArrayItem,} from '@angular/cdk/drag-drop';
import { LoaderService } from 'loader';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
 
@Component({
    selector: 'app-teams-dashboard',
    templateUrl: './teams-dashboard.component.html',
    styleUrls: ['./teams-dashboard.component.scss'],
    standalone: false
})
/*
  Teams Dashboard
*/
export class TeamsDashboardComponent implements OnInit {
  selectedTeam: string = '';
  selectedProjectLead: string = '';

  showAllTeams = false;
  showAllManagers = false;

  accordionState: { [key: number]: boolean } = {};
  innerAccordionState: { [teamIndex: number]: { [key: string]: boolean } } = {};

  @ViewChildren('projectDropSection')
  projectDropSections!: QueryList<ElementRef>;

  uniqueTeams: string[] = [];
  uniqueProjectLeads: string[] = [];
  filteredTeams: Team[] = [];
  teams: Team[] = [];
  originalFilteredTeams: Team[] = [];
  memberList:any[]=[];
  categoryList:any[]=[];

  constructor(
    private modalService: NzModalService,
    private loaderService: LoaderService,
    private onePmService: OnepmServiceService,
    private notification: NzNotificationService
  ) {}

  ngOnInit(): void {
    this.getTeamsData();
    this.getTeamsMasterData();
  }
  /**
   * Function to get teams data
   */
  getTeamsData() {
    this.loaderService.invokeLoaderComponent(true);
    this.teams = [];
    this.filteredTeams = [];
    this.originalFilteredTeams = [];
    this.uniqueTeams = [];
    this.uniqueProjectLeads = [];
    this.innerAccordionState = {};
    this.onePmService.getTeamsData().subscribe(
      (res: any) => {
        if (res.succeeded) {
        this.teams = res.data.teams;
        this.filteredTeams = this.teams;
        if(this.selectedTeam){
          this.filterByTeam(this.selectedTeam);
        }
        else if(this.selectedProjectLead){
          this.filterByProjectLead(this.selectedProjectLead);
        }
        else{
          this.originalFilteredTeams = JSON.parse(JSON.stringify(this.filteredTeams));
        }
        this.originalFilteredTeams = JSON.parse(JSON.stringify(this.filteredTeams));
        this.uniqueTeams = [...new Set(this.teams.map((team) => team.teamName))];
        this.uniqueProjectLeads = [
          ...new Set(this.teams.map((team) => team.teamDescription)),
        ];
        this.loaderService.invokeLoaderComponent(false);
        }
        else {
          this.teams = [];
          this.loaderService.invokeLoaderComponent(false);
          this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      },
      (err) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', err.message, {
          nzPlacement: 'bottomLeft',
          nzClass: 'error',
        });
      }
    );
  }
  /**
   * Function to get teams master data from api
   */
  getTeamsMasterData() {    
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getTeamsMasterData().subscribe(
      (res: any) => {
        if (res.succeeded) {
          this.memberList = res.data.members;
          this.categoryList = res.data.category;
          this.loaderService.invokeLoaderComponent(false);
        } else {
          this.memberList =[];
          this.categoryList =[];
          this.loaderService.invokeLoaderComponent(false);
          this.notification.blank('', res.message, {
            nzPlacement: 'bottomLeft',
            nzClass: 'error',
          });
        }
      },
      (err) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', err.message, {
          nzPlacement: 'bottomLeft',
          nzClass: 'error',
        });
      }
    );
  }
  /**
   * Function to reset filter
   */
  resetFilter(): void {
    this.selectedTeam = '';
    this.selectedProjectLead = '';
    this.filteredTeams = this.teams;
    this.originalFilteredTeams = JSON.parse(JSON.stringify(this.filteredTeams));
  }
 /**
   * Function to filter by team 
   * @param teamName Selected team name
   */
  filterByTeam(teamName: string): void {
    this.selectedTeam = teamName;
    this.selectedProjectLead = '';
    this.filteredTeams = this.teams.filter((team) => team.teamName === teamName);
    this.originalFilteredTeams = JSON.parse(JSON.stringify(this.filteredTeams));
  }
  /**
   * Function to filter by Project lead 
   * @param projectLead Selected Project lead
   */
  filterByProjectLead(projectLead: string): void {
    this.selectedProjectLead = projectLead;
    this.selectedTeam = '';
    this.filteredTeams = this.teams.filter(
      (team) => team.teamDescription === projectLead
    );
    this.originalFilteredTeams = JSON.parse(JSON.stringify(this.filteredTeams));
  }
  /**
   * Function to get  team count 
   * @param teamName Team name
   */
  getTeamCount(teamName: string): number {
    return this.teams.filter((team) => team.teamName === teamName).length;
  }
  /**
   * Function to get  project lead count 
   * @param projectLead projectLead
   */
  getProjectLeadCount(projectLead: string): number {
    return this.teams.filter((team) => team.teamDescription === projectLead)
      .length;
  }
  /**
   * Function to get find out team with its index
   * @param teamName team name
   * @param key team index number
   */
  getSanitizedId(teamName: string, key?: any): string {
    return (teamName + key).replace(/\s+/g, '-');
  }
  /**
   * Function for add team member  
   * @param team team member details
   */
  addTeamMember(team: any): void {
    const modal: NzModalRef = this.modalService.create({
      nzTitle: 'Add Team Member',
      nzContent: AddMemberComponent,
      nzData: {
        data: {
          teamId:team.teamId,
          memberList:this.memberList,
          categoryList:this.categoryList,
        },
      },
      nzFooter: null,
      nzMaskClosable: false,
    });
    setTimeout(() => {
      const componentInstance =
        modal.getContentComponent() as AddMemberComponent;
      modal.updateConfig({
        nzFooter: componentInstance.modalFooterTemplate,
      });
    });

    modal.afterClose.subscribe((result: any) => {
      if(result){
        this.getTeamsData();
      }
      // this.afterModalClose(result);
    });
  }
  /**
   * Function for add team  
   */
  addTeam() {
    const modal: NzModalRef = this.modalService.create({
      nzTitle: 'Add Team',
      nzContent: AddTeamComponent,
      nzFooter: null,
      nzMaskClosable: false,
    });
    setTimeout(() => {
      const componentInstance = modal.getContentComponent() as AddTeamComponent;
      modal.updateConfig({
        nzFooter: componentInstance.modalFooterTemplate,
      });
    });
    modal.afterClose.subscribe((result: any) => {
      if(result){
        this.resetFilter();
        this.getTeamsData();
      }
    });
  }
  /**
   * Function for add project  
   * @param team team member details
   */
  addProject(team: any): void {
    const modal: NzModalRef = this.modalService.create({
      nzTitle: 'Add New Project',
      nzContent: AddProjectComponent,
      nzData: {
        data: {
          teamId:team.teamId,
        },
      },
      nzFooter: null,
      nzMaskClosable: false,
    });
    setTimeout(() => {
      const componentInstance =
        modal.getContentComponent() as AddProjectComponent;
      modal.updateConfig({
        nzFooter: componentInstance.modalFooterTemplate,
      });
    });
    modal.afterClose.subscribe((result: any) => {
      if(result){
        this.getTeamsData();
      }      
    });
  }
   /**
   * Function for delete team member  
   * @param member member ID
   * @param teamId team ID
   */
  deleteTeamMember(member: any,teamId:any) {
    this.modalService.confirm({
      nzTitle: '',
      nzContent: 'Are you sure you want to delete team member?',
      nzOkText: 'OK',
      nzCancelText: 'Cancel',
      nzClosable: false,
      nzOnOk: () => {
        this.deleteTeamMemberExe(teamId,member.memberId)
      },
      nzOnCancel: () => {
        this.modalService.closeAll();
      },
    });
  }
  /**
   * Function for toggle main accordion  
   * @param index team index
   */
  toggleAccordion(index: number) {
    this.accordionState[index] = !this.accordionState[index];
  }
  /**
   * Function for toggle inner accordion  
   * @param teamIndex team index
   * @param key category
   */
  toggleInnerAccordion(teamIndex: number, key: string) {
    if (!this.innerAccordionState[teamIndex]) {
      this.innerAccordionState[teamIndex] = {};
    }
    this.innerAccordionState[teamIndex][key] =
      !this.innerAccordionState[teamIndex][key];
  }
  /**
   * Function for drop project  
   * @param event cdk drop event
   * @param projects Project array
   * @param teamIndex team index
   */
  drop(event: CdkDragDrop<any[]>, projects: any[], teamIndex: number) {
    if (event.previousContainer === event.container) {
      moveItemInArray(
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
    projects.forEach((project, index) => {
      project.priority = index + 1;
    });
  }
  /**
   * Function Retrieve original and current projects based on priority order  
   * @param teamIndex team index
   */
  isProjectModified(teamIndex: number): boolean {
    const originalProjects = this.originalFilteredTeams[teamIndex].projects.map(
      (project) => project.projectName
    );
    const currentProjects = this.filteredTeams[teamIndex].projects.map(
      (project) => project.projectName
    );

    // Check if the order has changed
    return !originalProjects.every(
      (name, index) => name === currentProjects[index]
    );
  }
  /**
   * Function for delete team 
   * @param teamId team ID
   */
  deleteTeam(teamId: any) {
    this.modalService.confirm({
      nzTitle: '',
      nzContent: 'Are you sure you want to delete team?',
      nzOkText: 'OK',
      nzCancelText: 'Cancel',
      nzClosable: false,
      nzOnOk: () => {
        this.deleteTeamExe(teamId);
      },
      nzOnCancel: () => {
        this.modalService.closeAll();
      },
    });
  }
  /**
   * Function for delete team member external
   * @param teamId team ID
   * @param memberId member ID
   */
  deleteTeamMemberExe(teamId:number,memberId:number){
    this.loaderService.invokeLoaderComponent(true);
    let userId = localStorage.getItem('userEmail');
    let body = {
      teamId: teamId,
      memberId:memberId,
      userId: userId
    };
    this.onePmService.deleteTeamMemberData(body).subscribe(
      (res) => {
        this.loaderService.invokeLoaderComponent(false);
        if(res.succeeded){
        this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'success' });
        this.getTeamsData();
        }  else {
          this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      },
      (error) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    );
  }
  /**
   * Function for delete team external
   * @param teamId team ID
   */
  deleteTeamExe(id:number){
    this.loaderService.invokeLoaderComponent(true);
    let userId = localStorage.getItem('userEmail');
    let body = {
      teamId: id,
      userId: userId
    };
    this.onePmService.deleteTeamsData(body).subscribe(
      (res) => {
        this.loaderService.invokeLoaderComponent(false);
        if(res.succeeded){
        this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'success' });
        this.resetFilter();
        this.getTeamsData();
        }  else {
          this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      },
      (error) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    );
  } 
  /**
   * Function for delete project
   * @param projectId project ID
   * @param teamId team ID
   */
  deleteProject(projectId:number,teamId:number){
    this.modalService.confirm({
      nzTitle: '',
      nzContent: 'Are you sure you want to delete project?',
      nzOkText: 'OK',
      nzCancelText: 'Cancel',
      nzClosable: false,
      nzOnOk: () => {
        this.deleteProjectExe(projectId,teamId);
      },
      nzOnCancel: () => {
        this.modalService.closeAll();
      },
    });

  }
   /**
   * Function for delete project external
   * @param projectId project ID
   * @param teamId team ID
   */
  deleteProjectExe(projectId:number,teamId:number){
    this.loaderService.invokeLoaderComponent(true);
    let userId = localStorage.getItem('userEmail');    
    let body = {
      teamId: teamId,
      projectId: projectId,
      userId: userId
    };
    this.onePmService.deleteProjectData(body).subscribe(
      (res) => {
        this.loaderService.invokeLoaderComponent(false);
        if(res.succeeded){
        this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'success' });
        this.getTeamsData();
        }  else {
          this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      },
      (error) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    );
  }
  /**
   * Function for save project priority order
   * @param projectId project ID
   * @param teamId team ID
   */
  saveUpdatedProject(team:Team){
    this.loaderService.invokeLoaderComponent(true);
    let userId = localStorage.getItem('userEmail');
    const updatedPriority = team.projects.map(({ projectName, ...rest }) => rest);
    let body = {
      projectPriority: updatedPriority,
      userId:userId      
    };    
    this.onePmService.updateProjectData(body).subscribe(
      (res) => {
        this.loaderService.invokeLoaderComponent(false);
        if(res.succeeded){
        this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'success' });
        this.getTeamsData();
        }  else {
          this.notification.blank('', res?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
        }
      },
      (error) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    );
  }
}
 