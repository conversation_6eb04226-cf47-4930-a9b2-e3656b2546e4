import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { HeaderConstant } from './header.constant';
import { MsalService } from '@azure/msal-angular';
import { AuthService } from 'src/app/service/auth.service';
import { Router } from '@angular/router';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss'],
    standalone: false
})
export class HeaderComponent implements OnInit {
  headerConstant = HeaderConstant;
  @Input() userName: string | null = '';
  iconType = '';
  isBoardType:boolean = false;
  isManagementType:boolean = false;
  isMenuOpen:boolean = false;
  isMenuManagementOpen:boolean = false;
  breadcrumbData!: string;
  currentRoute!: string;
  @Output() logout = new EventEmitter();

  constructor(private msalService: MsalService, private authService: AuthService, private router: Router) {
    this.router.events.subscribe(() => {
      this.currentRoute = this.router.url;
      if (this.currentRoute.includes('project-dashboard')) {
        this.iconType = 'project'; this.breadcrumbData = 'Project Dashboard';this.isBoardType = false;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('daily-dashboard')) {
        this.iconType = 'daily'; this.breadcrumbData = 'Daily Dashboard';this.isBoardType = false;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('pmo-dashboard')) {
        this.iconType = 'pmo'; this.breadcrumbData = 'PMO Dashboard';this.isBoardType = true;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('kpi-dashboard')) {
        this.iconType = 'kpi'; this.breadcrumbData = 'KPI Dashboard';this.isBoardType = true;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('teams-dashboard')) {
        this.iconType = 'teams'; this.breadcrumbData = 'Teams Dashboard';this.isBoardType = true;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('utilization-dashboard')) {
        this.iconType = 'utilization'; this.breadcrumbData = 'Utilization Dashboard';this.isBoardType = true;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('/jira-timelog')) {
        this.iconType = 'jiratimelog'; this.breadcrumbData = 'Jira Timelog Report';this.isBoardType = true;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('/ba-reporting')) {
        this.iconType = 'baReporting'; this.breadcrumbData = 'BA Reporting';this.isBoardType = true;this.isManagementType = false;
      }
      else if (this.currentRoute.includes('/change-management-dashboard')) {
        this.iconType = 'changeManagement'; this.breadcrumbData = 'Change Management Dashboard';this.isBoardType = false;this.isManagementType = true;
      }
      else if (this.currentRoute.includes('/executive-dashboard')) {
        this.iconType = 'executive'; this.breadcrumbData = 'Executive Dashboard';this.isBoardType = false;this.isManagementType = true;
      }
    });
  }

  ngOnInit(): void {
  }

  logoutRequst() { this.logout.emit(); }

  /**
   * Is Logged In
   * @returns
   */
  isLoggedIn() {
    if (this.msalService.instance.getActiveAccount() != null) {
      this.userName = localStorage.getItem('userName');
      return true
    } else return false
  }
  onClickBoardIcon(){
    this.isMenuOpen = true;
  }
  onClickManagementIcon(){
    this.isMenuManagementOpen = true;
  }
  onClickIcon(type: string) {
    this.iconType = type;
    this.isBoardType = this.iconType == 'daily' || this.iconType =='project' ? false:true;
    this.isManagementType = this.iconType == 'daily' || this.iconType =='project' ? false:true;
    this.isMenuOpen = false;
    this.isMenuManagementOpen = false;
  }

  haveOnePmMenuAccess(capabilityGroupName: string, capabilityName: string): boolean {
    return this.authService.haveViewAccess(capabilityGroupName, capabilityName);
  }
  gotoHome(capabilityGroupName: string, capabilityName: string) {
    if (this.authService.haveViewAccess(capabilityGroupName, capabilityName)) {
      this.router.navigate(['/daily-dashboard'])
    }
  }

  checkMenuAccess(): boolean {
    return (this.haveOnePmMenuAccess('App_Page', 'PMO_Dashboard') ||
    this.haveOnePmMenuAccess('App_Page', 'KPI_Dashboard') ||
    this.haveOnePmMenuAccess('App_Page', 'Teams_Dashboard') ||
    this.haveOnePmMenuAccess('App_Page', 'Utilization_Dashboard') ||
    this.haveOnePmMenuAccess('App_Page', 'Jira_Timelog_Report') ||
    this.haveOnePmMenuAccess('App_Page', 'BA_Reporting_Dashboard'))
  }
  checkManagementMenuAccess(): boolean {
    return (this.haveOnePmMenuAccess('App_Page', 'Change_Management_Dashboard') ||
    this.haveOnePmMenuAccess('App_Page', 'Executive_Dashboard'))
  }

}
