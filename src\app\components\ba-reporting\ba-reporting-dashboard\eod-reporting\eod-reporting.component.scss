::ng-deep {
    .bg-accordian-button-custom {
        .ant-collapse-header {
            color: white !important;
        }
 
        background-color: rgb(84, 137, 187) !important;
 
        .ant-collapse-content-box {
            padding: 2px !important;
            overflow-x: hidden !important;
        }
    }
}
 
::ng-deep .ant-collapse-content-box {
    padding: 8px !important;
    /* Adjust padding */
    min-height: 20px !important;
    /* Set desired minimum height */
}
 
::ng-deep .ant-collapse-header {
    padding: 8px !important;
    /* Adjust padding for header */
    min-height: 32px !important;
    /* Adjust header height */
}
::ng-deep .application-bugs-grid .wrap-header .ag-header-cell-label {
  white-space: normal !important;
  word-wrap: break-word !important;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sub-heading{
    color: #555;
}
table {
      border-collapse: collapse;
      width: 400px;
      text-align: center;
      font-family: 'Poppins', sans-serif !important;
 
    }
   
    th, td {
      border: 1px solid gray;
      font-size: 12px;
    }
 
    .header {
      font-weight: normal;
      font-size: 13px;
      td {
        font-size: 13px;
      }
    }
    .custom-spin-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* Ensures it takes full height of the parent */
}
.loading-text {
  color: #f28b82; /* Light red shade */
  font-weight: 500;
  font-size: 16px;
}
 