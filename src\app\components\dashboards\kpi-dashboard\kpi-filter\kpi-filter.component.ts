import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';
import moment from 'moment';
import { LoaderService } from 'loader';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { KpiExportService } from 'src/app/service/kpi-export.service';

@Component({
    selector: 'app-kpi-filter',
    templateUrl: './kpi-filter.component.html',
    styleUrls: ['./kpi-filter.component.scss'],
    standalone: false
})
/* 
  Filter component for KPI Dashboard, child of KPI Dashboard
*/
export class KpiFilterComponent implements OnInit {
  assetURL = environment.appBaseURL;
  kpiFilterForm!: FormGroup;
  @Output() emitFilters = new EventEmitter<any>();
  @Input() gridData: any;
  @Input() gridMode: any;
  viewData: any[] = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private notification: NzNotificationService,
    private loaderService: LoaderService,
    private onePmService: OnepmServiceService,
    private kpiExportService: KpiExportService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.getMasterData();
  }
  /**
   * Initializes the KPI filter form with required fields.
   * @returns FormGroup instance with view, startDate, and endDate fields.
   */
  initializeForm(): FormGroup {
    const { firstDay, today } = this.getCurrentMonthDates();
    return (this.kpiFilterForm = this.formBuilder.group({
      view: [null, Validators.required],
      startDate: [firstDay, Validators.required],
      endDate: [today, Validators.required],
    }));
  }
  /**
   * Handles the search action.
   * Emits selected filter data if the form is valid, otherwise shows an error notification.
   */
  onSearch() {
    if (this.kpiFilterForm.valid) {
      const startDate = this.changeDateFormats(
        this.kpiFilterForm.controls['startDate'].value
      );
      const endDate = this.changeDateFormats(
        this.kpiFilterForm.controls['endDate'].value
      );
      const selectedItem = this.kpiFilterForm.controls['view'].value;
      const payloadData = {
        type: selectedItem ? selectedItem : null,
        fromDate: startDate,
        toDate: endDate,
      };
      this.emitFilters.emit({ data: payloadData });
    } else {
      if(!this.kpiFilterForm.controls['view'].value || this.kpiFilterForm.controls['view'].value.length === 0){
        this.notification.blank('', 'Please select View', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      if(!this.kpiFilterForm.controls['startDate'].value){
        this.notification.blank('', 'Please select From Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      if(!this.kpiFilterForm.controls['endDate'].value){
        this.notification.blank('', 'Please select To Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
     
    }
  }
  /**
   * Converts a date object to the format 'YYYY-MM-DD'.
   * @param value Date object to be formatted.
   * @returns Formatted date string.
   */
  changeDateFormats(value: Date) {
    return moment(value).format('YYYY-MM-DD');
  }
  /**
   * Disables dates in the 'To Date' picker before the selected 'From Date'.
   * @param current Date to be checked.
   * @returns Boolean indicating whether the date should be disabled.
   */
  disableToDate = (current: Date): boolean => {
    const fromDate = this.kpiFilterForm.get('startDate')?.value;
    return fromDate ? moment(current).isBefore(moment(fromDate), 'day') : false;
  };
   /**
   * Disables dates in the 'From Date' picker after the selected 'To Date'.
   * @param current Date to be checked.
   * @returns Boolean indicating whether the date should be disabled.
   */
  disableFromDate = (current: Date): boolean => {
    const toDate = this.kpiFilterForm.get('endDate')?.value;
    return toDate ? moment(current).isAfter(moment(toDate), 'day') : false;
  };
  /**
   * Fetches master data for KPI filter dropdown and handles the API response.
   * Shows a loader while fetching data and displays a notification in case of an error.
   */
  getMasterData() {
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getMasterData().subscribe(
      (res: any) => {
        if (res.succeeded) {
          this.loaderService.invokeLoaderComponent(false);
          this.viewData = res.data.kpiType;
          this.kpiFilterForm.controls['view'].setValue(this.viewData[0].key);
          this.onSearch();
        } else {
          this.loaderService.invokeLoaderComponent(false);
          this.notification.blank('', res.message, {
            nzPlacement: 'bottomLeft',
            nzClass: 'error',
          });
        }
      },
      (err) => {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', err.message, {
          nzPlacement: 'bottomLeft',
          nzClass: 'error',
        });
      }
    );
  }
  /**
   * Clears the form and emits an event to indicate filter reset.
   */
  onClear() {
    this.kpiFilterForm.reset();
    this.emitFilters.emit({ clearFilter: true });
  }
  /**
   * Exports KPI data based on the selected grid mode.
   * Calls the appropriate export method from `KpiExportService`.
   */
  onExport() {
    switch (this.gridMode) {
      case 'Tickets_And_Hours_By_Project':
        this.kpiExportService.exportTicketsHours(this.gridData, this.gridMode);
        break;
      case 'Key_Features':
        this.kpiExportService.exportKeyFeatures(this.gridData, this.gridMode);
        break;
      case 'Deployment_Success':
        this.kpiExportService.exportDeploymentSuccess(this.gridData, this.gridMode);
        break;
      case 'Hours_Analysis':
        this.kpiExportService.exportHoursAnalysis(this.gridData, this.gridMode);
        break;
    }
  }
  /*
  * Displays To date and first day of current month  
  */
  getCurrentMonthDates(): { firstDay: Date; today: Date } {
  const today = new Date();
  const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
  return { firstDay, today };
}
}
