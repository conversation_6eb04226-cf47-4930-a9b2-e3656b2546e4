import { Component, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import moment from 'moment';
import { IDropdownSettings, MultiSelectComponent } from 'ng-multiselect-dropdown';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { environment } from 'src/environments/environment';
import { LoaderService } from 'loader';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { SharedService } from 'src/app/service/shared.service';


@Component({
    selector: 'app-project-dashboard-filter',
    templateUrl: './project-dashboard-filter.component.html',
    styleUrls: ['./project-dashboard-filter.component.scss'],
    standalone: false
})
export class ProjectDashboardFilterComponent implements OnInit {
  assetURL = environment.appBaseURL;
  projectFilterForm!: FormGroup;
  projectData: any[] = [];
  columnDefs: any[] = dashboardConstants.projectColDef;
  exportData: any[] = [];
  initialArray: any[] = [{
    key: 'All', value: 'All'
  }]
  @Input() masterData: any;
  @Input() excelData: any;
  @Output() emitFilters = new EventEmitter<any>();
  @ViewChildren('project') dropdowns!: QueryList<MultiSelectComponent>;
  constructor(private formBuilder: UntypedFormBuilder, private onePmService: OnepmServiceService, private loaderService: LoaderService, private notification: NzNotificationService, private sharedService: SharedService) { }
  /**
   * Lifecycle hook that is called after data-bound properties are initialized
  */
  ngOnInit(): void {
    this.initialForm();
    this.getMasterData();
  }
  /**
  * Lifecycle hook that is called after data-bound properties are initialized
 */
  ngOnChanges() {
    this.exportData = this.excelData;
  }
  /**
  * Function to get initialize formGroup
 */
  initialForm(): FormGroup {
    return this.projectFilterForm = this.formBuilder.group({
      project: [null, Validators.required],
      startDate: [this.getCurrentWeekDates().monday, Validators.required],
      endDate: [this.getCurrentWeekDates().friday, Validators.required]
    });
  }
  /**
  * Function to get Current week date (monday and friday for initializing default)
 */
  getCurrentWeekDates(): { monday: Date; friday: Date } {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const diffToFriday = dayOfWeek === 0 ? -2 : 5 - dayOfWeek;
    const monday = new Date(today);
    const friday = new Date(today);
    monday.setDate(today.getDate() + diffToMonday);
    friday.setDate(today.getDate() + diffToFriday);
    return { monday, friday };
  }
  /**
  * Function to get project list dropdown and legends list
 */
  getMasterData() {
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getMasterData().subscribe((res: any) => {
      if (res.succeeded) {
        this.loaderService.invokeLoaderComponent(false);
        this.projectData = this.initialArray.concat(res.data.projects);
        this.sharedService.legends = res.data?.legendsInfo;
        this.projectFilterForm.controls['project'].setValue([this.projectData[0]]);
        this.onSearch(false);
      }
      else {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
  /**
    * Function to set ng multiselect filter settings
    */
  filterSettings(
    singleSelection: boolean,
    enableCheckAll: boolean,
    allowSearchFilter: boolean,
    id: string,
    name: string): IDropdownSettings {
    return ({
      singleSelection: singleSelection,
      idField: id,
      textField: name,
      selectAllText: 'All',
      unSelectAllText: 'All',
      itemsShowLimit: 1,
      enableCheckAll: enableCheckAll,
      allowSearchFilter: allowSearchFilter,
    });
  }
  /**
   * Function to get select event of ng multiselect
   * @param event containing selected value
   */
  onItemSelect(event: any) {
    if (event.key == 'All')
      this.projectFilterForm.controls['project'].setValue([{ key: 'All', value: 'All' }])
    else
      this.projectFilterForm.controls['project'].setValue(this.projectFilterForm.controls['project'].value.filter((s: any) => s.key != 'All'))
  }
  /**
   * Function to close ng select dropdowns  while opening date picker
   */
  closeAllDropdowns(): void {
    this.dropdowns.forEach((dropdown) => {
      dropdown.closeDropdown();
    });
  }
  /**
  * Function to change event of date picker 
  */
  onOpenChange(isOpen: boolean): void {
    if (isOpen) this.closeAllDropdowns();
  }
  /**
  * Function to click event of Export button
  */
  onExportProject() {
    this.loaderService.invokeLoaderComponent(true);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Project Data');
    const title = 'Project_Dashboard_List' + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
    let headers: any = [];
    this.columnDefs.forEach(ele => {
      if (ele.headerName != '') headers.push(ele.headerName);
    });
    // Add Header Row
    worksheet.addRow(headers);

    // Style Header Row
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    });
    // Add Data Rows
    this.exportTreeDataToExcel(worksheet);
    // Adjust Column Widths
    worksheet.columns.forEach((column) => {
      worksheet.getColumn(2).width = 50;
      worksheet.getColumn(9).alignment = { horizontal: 'right' };
      column.width = 20; // Adjust based on content
    });
    // Add Legend Rows
    worksheet.addRow([]);
    worksheet.addRow(['Legends']).font = { bold: true, size: 14 };
    // worksheet.mergeCells('A1:E1');
    worksheet.getCell('A1').alignment = { vertical: 'middle', horizontal: 'center' };
    const legends = this.sharedService.legends;
    legends.forEach((legend: any, index: number) => {
      const row = worksheet.addRow([legend.category]);
      // Set Cell Styles
      const cell = row.getCell(1);
      this.onHighlightCell(cell, legend);
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
      // Set Row Height
      worksheet.getRow(row.number).height = 25;
    });

    // Export Workbook
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, title + '.xlsx');
    });
    this.loaderService.invokeLoaderComponent(false);
  }
  /**
 * Function to call constructing data to tree model 
 * @param data export data
 */
  buildTree(data: any[]) {
    const map = new Map<string, any>();
    const allKeys = new Set(data.map(item => item.issueKey));
    const roots: any[] = [];

    data.forEach(item => {
      map.set(item.issueKey, { ...item, children: [] });
    });

    map.forEach((item, key) => {
      if (item.masterKey && map.has(item.masterKey)) {
        const parent = map.get(item.masterKey);
        parent.children.push(item);
      } else {
        roots.push(item); // either no masterKey, or orphan (masterKey not found)
      }
    });

    return roots;
  }
  /**
  * Function call Excel data creations and operations
  * @param worksheet sheet name
  */
  exportTreeDataToExcel(worksheet: any) {
    const treeData = this.buildTree(this.exportData);
    const addRowsRecursively = (nodes: any[], level: number = 0) => {
      nodes.forEach(node => {
        const indent = '  '.repeat(level); // for visual indent in Excel
        const row = worksheet.addRow([
          indent + node.issueKey,
          node.description,
          node.issueKeyType === 'Epic' ? '' : node.assignee,
          node.startDate ? this.displayExportDateFormats(node.startDate) : '',
          node.issueKeyType === 'Epic' ? '' : node.dueDateInfo?.date ? this.displayExportDateFormats(node.dueDateInfo.date) : '',
          node.issueKeyType === 'Epic' ? this.getEpicStatus(node.issueKey) : node.developmentStage,
          this.getTotalHours(node.issueKey),
          this.getTotalRemainingHours(node.issueKey),
          this.getPercentageHoursComplete(node.issueKey),
        ]);

        // Optionally style rows by level (indent or font)
        row.getCell(1).alignment = { indent: level }; // indent Issue Key cell

        // Highlight Due Date and Worklog cells
        this.onHighlightCell(row.getCell(5), node.dueDateInfo);
        this.onHighlightCell(row.getCell(7), node.workLogInfo);

        // Recurse into children
        if (node.children?.length) {
          addRowsRecursively(node.children, level + 1);
        }
      });
    };
    addRowsRecursively(treeData);
  }
  /**
   * filter matching master keys
   * @param masterKey parent field
  */
  getChildren(masterKey: string): any[] {
    return this.exportData.filter(row => row.masterKey === masterKey);
  }
  /**
   * Calculating Total Hours
   * @param issueKey field of the table
  */
  getTotalHours(issueKey: string): number {
    const children = this.getChildren(issueKey);

    if (children.length === 0) {
      // Leaf node — return its own hours
      const row = this.exportData.find(r => r.issueKey === issueKey);
      return row?.workLogInfo?.hours ?? 0;
    }

    // Parent — sum up children's hours
    let total = 0;
    for (const child of children) {
      total += this.getTotalHours(child.issueKey);
    }
    return total;
  }
  /**
  * Calculating Remaining Hours
  * @param issueKey field of the table
 */
  getTotalRemainingHours(issueKey: string): number {
    const children = this.getChildren(issueKey);

    if (children.length === 0) {
      // Leaf node — return its own remaining hours
      const row = this.exportData.find(r => r.issueKey === issueKey);
      return row?.remainingHours ?? 0;
    }

    // Parent — sum up children's remaining hours
    let total = 0;
    for (const child of children) {
      total += this.getTotalRemainingHours(child.issueKey);
    }
    return total;
  }
  /**
   * Calculating Percentage Calculate Hours
   * @param issueKey field of the table
  */
  getPercentageHoursComplete(issueKey: string): string {
    const totalHours = this.getTotalHours(issueKey);
    const totalRemaining = this.getTotalRemainingHours(issueKey);

    const denominator = totalRemaining + totalHours;

    if (denominator === 0) {
      return '0%'; // Avoid division by zero
    }

    const percentage = (totalRemaining / denominator) * 100;

    // return `${percentage.toFixed(1)}%`;
    return Number.isInteger(percentage) ? `${percentage}%` : `${percentage.toFixed(1)}%`;
  }
  /**
  * Calculating Status Calculate Hours
  * @param issueKey field of the table
  *
 */
  getEpicStatus(epicKey: string): string {
    const children = this.getChildren(epicKey);
    const allIssues: any[] = [];

    children.forEach(child => {
      allIssues.push(child);
      const grandChildren = this.getChildren(child.issueKey);
      allIssues.push(...grandChildren);
    });

    if (allIssues.length === 0) return 'In Progress';

    const allDone = allIssues.every(issue => issue.developmentStage === 'Done - Close');
    return allDone ? 'Done' : 'In Progress';
  }
  /**
   * Function to call  highlight cell 
   * @param cell Containing Cell details
   * @param data Containing binding details
   */
  onHighlightCell(cell: any, data: any) {
    if (data.backgroundColor) {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: data.backgroundColor.replace('#', '') },
      };
    }
    if (data.fontColor) {
      cell.font = { color: { argb: data.fontColor.replace('#', '') } };
    }
    if (data.borderColor) {
      cell.border = {
        top: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        left: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        bottom: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        right: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
      };
    }
  }
  /**
   * Function to Convert html content to bullets
   * @param html Contain Html contents
   */
  convertHtmlToBullets(html: string): string {
    let result = html.replace(/<\/?ul>/g, '\r\n');
    result = result.replace(/<li>/g, '• ').replace(/<\/li>/g, '\r\n');
    return result.trim();
  }
  /**
   * Function to Change date format 
   * @param value Contain date
   */
  changeDateFormats(value: Date) {
    return moment(value).format('YYYY-MM-DD')
  }
  /**
   * Function to Change date format 
   * @param value Contain date
   */
  displayExportDateFormats(value: Date) {
    return moment(value).format('MM-DD-YYYY')
  }
  /**
     * Disables dates in the 'To Date' picker before the selected 'From Date'.
     * @param current Date to be checked.
     * @returns Boolean indicating whether the date should be disabled.
     */
  disableToDate = (current: Date): boolean => {
    const fromDate = this.projectFilterForm.get('startDate')?.value;
    return fromDate ? moment(current).isBefore(moment(fromDate), 'day') : false;
  };
  /**
 * Disables dates in the 'From Date' picker after the selected 'To Date'.
 * @param current Date to be checked.
 * @returns Boolean indicating whether the date should be disabled.
 */
  disableFromDate = (current: Date): boolean => {
    const toDate = this.projectFilterForm.get('endDate')?.value;
    return toDate ? moment(current).isAfter(moment(toDate), 'day') : false;
  };
  /**
   * Event of to Clear Button
   */
  onClearFilter() {
    this.projectFilterForm.reset();
  }
  /**
  * Event of to Search Button
  * @param clear contain Clear button touched or not
  */
  onSearch(clear?: boolean) {
    if (this.projectFilterForm.valid) {
      const startDate = this.changeDateFormats(this.projectFilterForm.controls['startDate'].value);
      const endDate = this.changeDateFormats(this.projectFilterForm.controls['endDate'].value);
      const selectedItem = this.projectFilterForm.controls['project'].value?.map((item: any) => item['key']).join(',');
      const payloadData = {
        project: selectedItem ? selectedItem : null,
        startDate: startDate,
        endDate: endDate
      }
      this.emitFilters.emit({ data: payloadData, clearFilter: clear });
    } else { 
      if(!this.projectFilterForm.controls['startDate'].value){
        this.notification.blank('', 'Please select From Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      if(!this.projectFilterForm.controls['endDate'].value){
        this.notification.blank('', 'Please select To Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      if(!this.projectFilterForm.controls['project'].value || this.projectFilterForm.controls['project'].value.length === 0){
        this.notification.blank('', 'Please select at least one Project', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
    }
  }
}
