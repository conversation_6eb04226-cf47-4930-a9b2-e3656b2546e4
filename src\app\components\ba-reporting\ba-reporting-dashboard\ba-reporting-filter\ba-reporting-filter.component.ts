import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import moment from 'moment';


@Component({
    selector: 'app-ba-reporting-filter',
    templateUrl: './ba-reporting-filter.component.html',
    styleUrls: ['./ba-reporting-filter.component.scss'],
    standalone: false
})
/* 
  Component for ba reporting filter, child of ba reporting Dashboard
*/
export class BaReportingFilterComponent implements OnInit {
  assetURL = environment.appBaseURL;
  baFilterForm!: FormGroup;
  @Output() emitFilters = new EventEmitter<any>();
  @Input() gridData: any;
  @Input() gridMode: any;
  reportData: any[] = [
    {key:'PMO_EOD_REPORTING',value:'PMO EOD Reporting'},
    {key:'EOD_REPORTING',value:'BA EOD Reporting'},
  ];
  constructor(
       private formBuilder: UntypedFormBuilder,
        private notification: NzNotificationService,
  ) { }

  ngOnInit(): void {
    this.initializeForm();
  }
  /**
   * Initializes the KPI filter form with required fields.
   * @returns FormGroup instance with view, startDate, and endDate fields.
   */
  initializeForm(): FormGroup {
    return (this.baFilterForm = this.formBuilder.group({
      report: ['', Validators.required],
      startDate: [new Date(), Validators.required],
      // endDate: [null, Validators.required],
    }));
  }
  /**
   * Handles the search action.
   * Emits selected filter data if the form is valid, otherwise shows an error notification.
   */
  onSearch() {
      if (this.baFilterForm.valid) {
        const startDate = this.changeDateFormats(
          this.baFilterForm.controls['startDate'].value
        );
        const selectedItem = this.baFilterForm.controls['report'].value;
        const payloadData = {
          report: selectedItem ? selectedItem : null,
          date: startDate,
        };
        this.emitFilters.emit({ data: payloadData });
      } else {
        if(!this.baFilterForm.controls['report'].value || this.baFilterForm.controls['report'].value.length === 0){
          this.notification.blank('', 'Please select Report', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
        }
        if(!this.baFilterForm.controls['startDate'].value){
        this.notification.blank('', 'Please select Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
        }
      }
    }
     /**
   * Clears the form and emits an event to indicate filter reset.
   */
    onClear() {
      this.baFilterForm.reset();
      this.emitFilters.emit({ clearFilter: true });
    }
    /**
     * Converts a date object to the format 'YYYY-MM-DD'.
     * @param value Date object to be formatted.
     * @returns Formatted date string.
     */
    changeDateFormats(value: Date) {
      return moment(value).format('YYYY-MM-DD');
    }
    /**
     * Disables dates in the 'To Date' picker before the selected 'From Date'.
     * @param current Date to be checked.
     * @returns Boolean indicating whether the date should be disabled.
     */
    disableToDate = (current: Date): boolean => {
      const fromDate = this.baFilterForm.get('startDate')?.value;
      return fromDate ? moment(current).isBefore(moment(fromDate), 'day') : false;
    };
     /**
     * Disables dates in the 'From Date' picker after the selected 'To Date'.
     * @param current Date to be checked.
     * @returns Boolean indicating whether the date should be disabled.
     */
    disableFromDate = (current: Date): boolean => {
      const toDate = this.baFilterForm.get('endDate')?.value;
      return toDate ? moment(current).isAfter(moment(toDate), 'day') : false;
    };
}
