<div class="row">
  <nz-collapse [nzAccordion]="false" [nzExpandIconPosition]="'end'" class="border-0 bg-transparent">
    <nz-collapse-panel nzHeader="All Application Bugs" class="bg-accordian-button-custom" [nzActive]="true">
      <nz-spin [nzSpinning]="isApplicationBugLoader">
        <!-- <nz-spin [nzSpinning]="!isApplicationBugLoader" [nzIndicator]="customIndicator"> -->
        <app-common-eodreport [style]="'width: 100%; height: 250px;'" [class]="'ag-theme-alpine application-bugs-grid'"
          [columnDefs]="applicationBugColDef" [rowData]="allApplicationBugs"
          [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="50"
        [overlayNoRowsTemplate]="noRowsTemplate" [rowHeight]="20"></app-common-eodreport>
      <!-- </nz-spin>                 -->
    </nz-spin>
  </nz-collapse-panel>
  <nz-collapse-panel nzHeader="Active Change Management Request" class="bg-accordian-button-custom" [nzActive]="true">
    <nz-spin [nzSpinning]="isApplicationBugLoader">
      <app-common-eodreport [style]="'width: 100%; height: 250px;'"
        [class]="'ag-theme-alpine application-bugs-grid'" [columnDefs]="changeManagementColDef" [rowData]="activeChangeManagementRequests"
        [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="50"
        [overlayNoRowsTemplate]="noRowsTemplate"
      [rowHeight]="20"></app-common-eodreport>
    </nz-spin>
  </nz-collapse-panel>
  <nz-collapse-panel nzHeader="Daily Support Cases" class="bg-accordian-button-custom" [nzActive]="true">
    <div class="m-2 sub-heading">Currently Open</div>

    <nz-spin [nzSpinning]="finalLoader">
      <app-common-eodreport [style]="'width: 100%; height: 250px;'" [class]="'ag-theme-alpine'"
        [columnDefs]="currentlyOpenCasesColDef" [rowData]="currentlyOpenCases"
        [overlayNoRowsTemplate]="noRowsTemplate"
        [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="30"
      [rowHeight]="20"></app-common-eodreport>
    </nz-spin>

    <div class="m-2 sub-heading">Closed Today</div>

    <nz-spin [nzSpinning]="finalLoader">
      <app-common-eodreport [style]="'width: 100%; height: 250px;'" [class]="'ag-theme-alpine'"
        [columnDefs]="closedColDef" [rowData]="closedCases"
        [overlayNoRowsTemplate]="noRowsTemplate"
        [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="30"
      [rowHeight]="20"></app-common-eodreport>
    </nz-spin>

    <div class="m-2 sub-heading">Open Today</div>
    <nz-spin [nzSpinning]="finalLoader">
      <app-common-eodreport [style]="'width: 100%; height: 250px;'" [class]="'ag-theme-alpine'"
        [columnDefs]="openedTodayColDef" [rowData]="openedTodayCases"
        [overlayNoRowsTemplate]="noRowsTemplate"
        [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="30"
      [rowHeight]="20"></app-common-eodreport>
    </nz-spin>

  </nz-collapse-panel>
  <nz-collapse-panel nzHeader="Salesforce Cases" class="bg-accordian-button-custom" [nzActive]="true">
    <div class="m-2 sub-heading">Development Priorities</div>

    <nz-spin [nzSpinning]="finalLoader">
      <app-common-eodreport [style]="'width: 100%; height: 250px;'" [class]="'ag-theme-alpine'"
        [columnDefs]="salesforceColDef" [rowData]="salesforceCases"
        [overlayNoRowsTemplate]="noRowsTemplate"
        [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="30"
      [rowHeight]="20"></app-common-eodreport>
    </nz-spin>

    <div class="m-2 sub-heading">Support Priorities</div>

    <nz-spin [nzSpinning]="finalLoader">
      <app-common-eodreport [style]="'width: 100%; height: 250px;'" [class]="'ag-theme-alpine'"
        [columnDefs]="salesForceSpportColDef" [rowData]="salesForceSupportCases"
        [overlayNoRowsTemplate]="noRowsTemplate"
        [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="30"
      [rowHeight]="20"></app-common-eodreport>
    </nz-spin>

  </nz-collapse-panel>
  <nz-collapse-panel nzHeader="oneBUTTON" class="bg-accordian-button-custom" [nzActive]="true">
    @if (!finalLoader) {
      <div class="row">
        <div class="m-2 sub-heading">Total Contracts To date since (10/23):<span><strong>{{oneButtonTotal?.contractCreationTraditionalMetrics?.totalContractsToDate}}</strong></span>
      </div>
    </div>
    <div class="row">
      <div class="m-2 sub-heading">{{ oneButtonTotal?.contractCreationTraditionalMetrics?.heading}}</div>
    </div>
    <div class="row">
      <table class="mx-3">
        <tr>
          <td colspan="3" class="header">Quotes</td>
        </tr>
        <tr class="header">
          <td>Quotes/Day</td>
          <td>Total Quotes Today</td>
          <td>Total Quotes This Month</td>
        </tr>
        <tr>
          <td>{{ oneButtonTotal?.contractCreationTraditionalMetrics?.quotes?.perDay }}</td>
          <td>{{ oneButtonTotal?.contractCreationTraditionalMetrics?.quotes?.totalToday }}</td>
          <td>{{ oneButtonTotal?.contractCreationTraditionalMetrics?.quotes?.totalThisMonth }}</td>
        </tr>
        <tr>
          <td colspan="3" class="header">Contracts</td>
        </tr>
        <tr class="header">
          <td>Contracts/Day</td>
          <td>Total Contracts Today</td>
          <td>Total Contracts This Month</td>
        </tr>
        <tr>
          <td>{{ oneButtonTotal?.contractCreationTraditionalMetrics?.contracts?.perDay }}</td>
          <td>{{ oneButtonTotal?.contractCreationTraditionalMetrics?.contracts?.totalToday }}</td>
          <td>{{ oneButtonTotal?.contractCreationTraditionalMetrics?.contracts?.totalThisMonth }}</td>
        </tr>
      </table>
    </div>
    <div class="row">
      <div class="m-2 sub-heading">{{oneButtonTotal?.currentMonthsRoofingTeamMetrics?.heading}}</div>
    </div>
    <div class="row">
      <table class="mx-3">
        <tr>
          <td colspan="3" class="header">Quotes</td>
        </tr>
        <tr class="header">
          <td>Quotes/Day</td>
          <td>Total Quotes Today</td>
          <td>Total Quotes This Month</td>
        </tr>
        <tr>
          <td>{{ oneButtonTotal?.currentMonthsRoofingTeamMetrics?.quotes?.perDay }}</td>
          <td>{{ oneButtonTotal?.currentMonthsRoofingTeamMetrics?.quotes?.totalToday }}</td>
          <td>{{ oneButtonTotal?.currentMonthsRoofingTeamMetrics?.quotes?.totalThisMonth }}</td>
        </tr>
        <tr>
          <td colspan="3" class="header">Contracts</td>
        </tr>
        <tr class="header">
          <td>Contracts/Day</td>
          <td>Total Contracts Today</td>
          <td>Total Contracts This Month</td>
        </tr>
        <tr>
          <td>{{ oneButtonTotal?.currentMonthsRoofingTeamMetrics?.contracts?.perDay }}</td>
          <td>{{ oneButtonTotal?.currentMonthsRoofingTeamMetrics?.contracts?.totalToday }}</td>
          <td>{{ oneButtonTotal?.currentMonthsRoofingTeamMetrics?.contracts?.totalThisMonth }}</td>
        </tr>
      </table>
    </div>
  }

  <div class="row mt-2">
    <div class="m-2 sub-heading">Development Priorities</div>
  </div>
  <nz-spin [nzSpinning]="finalLoader">
    <app-common-eodreport [style]="'width: 100%; height: 250px;'" [class]="'ag-theme-alpine'"
      [columnDefs]="oneButtonDevPrioritiesColdef" [rowData]="oneButtonPriorityData"
      [overlayNoRowsTemplate]="noRowsTemplate"
      [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="30"
    [rowHeight]="20"></app-common-eodreport>
  </nz-spin>
</nz-collapse-panel>
<nz-collapse-panel nzHeader="oneDRAW" class="bg-accordian-button-custom" [nzActive]="true">

  <nz-spin [nzSpinning]="finalLoader">
    <app-common-eodreport [style]="'width: 40%; height: 200px;'" [class]="'ag-theme-alpine'"
      [columnDefs]="oneDrawColDef" [rowData]="oneDrawTotal"
      [overlayNoRowsTemplate]="noRowsTemplate"
      [defaultColDef]="{ resizable: true, sortable: true, filter: false }" [headerHeight]="30"
    [rowHeight]="20"></app-common-eodreport>
  </nz-spin>
</nz-collapse-panel>
<nz-collapse-panel nzHeader="BA Status Update" class="bg-accordian-button-custom" [nzActive]="true">

  <nz-spin [nzSpinning]="finalLoader">
    <app-common-eodreport [style]="'width: 100%; height: 250px;'"
      [class]="'ag-theme-alpine application-bugs-grid'" [columnDefs]="baStatusColDef" [rowData]="baStatusUpdateData"
      [overlayNoRowsTemplate]="noRowsTemplate"
      [defaultColDef]="{ resizable: true, sortable: true, filter: true }" [headerHeight]="50"
    [rowHeight]="20"></app-common-eodreport>
  </nz-spin>
</nz-collapse-panel>
</nz-collapse>
</div>
<ng-template #customIndicator>
  <div class="custom-spin-indicator">
    <span class="loading-text">Error in Loading Data</span>
  </div>
</ng-template>
