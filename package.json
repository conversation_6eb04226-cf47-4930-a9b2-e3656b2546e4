{"name": "btdashboard", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/common": "^19.2.14", "@angular/compiler": "^19.2.14", "@angular/core": "^19.2.14", "@angular/forms": "^19.2.14", "@angular/platform-browser": "^19.2.14", "@angular/platform-browser-dynamic": "^19.2.14", "@angular/router": "^19.2.14", "@ant-design/icons-angular": "^19.0.0", "@azure/msal-angular": "^4.0.15", "@azure/msal-browser": "^4.15.0", "@ctrl/tinycolor": "^4.1.0", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "ag-grid-angular": "^33.3.2", "ag-grid-community": "^33.3.2", "bootstrap": "^5.3.7", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "highcharts": "^12.3.0", "highcharts-angular": "^4.0.1", "moment": "^2.30.1", "ng-multiselect-dropdown": "^1.0.0", "ng-zorro-antd": "^19.3.1", "rxjs": "^7.8.2", "tslib": "^2.8.1", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular/cli": "~19.2.15", "@angular/compiler-cli": "^19.2.14", "@types/file-saver": "^2.0.5", "@types/jasmine": "^5.1.8", "jasmine-core": "^5.9.0", "karma": "6.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "5.8.3"}}