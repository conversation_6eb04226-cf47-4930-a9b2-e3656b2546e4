{"name": "btdashboard", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^20.1.2", "@angular/common": "^20.1.2", "@angular/compiler": "^20.1.2", "@angular/core": "^20.1.2", "@angular/forms": "^20.1.2", "@angular/localize": "^20.1.2", "@angular/platform-browser": "^20.1.2", "@angular/platform-browser-dynamic": "^20.1.2", "@angular/router": "^20.1.2", "@ant-design/icons-angular": "^20.0.0", "@azure/msal-angular": "^4.0.15", "@azure/msal-browser": "^4.15.0", "@ctrl/tinycolor": "^4.1.0", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "ag-grid-angular": "^33.3.2", "ag-grid-community": "^33.3.2", "bootstrap": "^5.3.7", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "highcharts": "^12.3.0", "highcharts-angular": "^4.0.1", "moment": "^2.30.1", "ng-multiselect-dropdown": "^1.0.0", "ng-zorro-antd": "^20.1.0", "rxjs": "^7.8.2", "tslib": "^2.8.1", "zone.js": "^0.15.1"}, "devDependencies": {"@angular/build": "^20.1.1", "@angular/cli": "~20.1.1", "@angular/compiler-cli": "^20.1.2", "@types/file-saver": "^2.0.5", "@types/jasmine": "^5.1.8", "jasmine-core": "^5.9.0", "karma": "6.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "5.8.3"}}