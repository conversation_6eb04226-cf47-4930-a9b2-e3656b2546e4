<div>
  <div class="row mb-1">
    <app-daily-dashboard-filter [masterData]="masterData" [excelData]="dailyList"
    (emitFilters)="getFilterChange($event)"></app-daily-dashboard-filter>
  </div>
  <div class="row">
    <app-daily-dashboard-grid [dailydashBoardColDef]="dailyDashboardColDef"
    [dailydashBoardRowData]="dailyFlag ? dailyList: []"></app-daily-dashboard-grid>
  </div>
</div>
<div class="sticky-buttons-group">
  @if (!isDivVisible) {
    <button nz-button nzType="primary" (click)="toggleDiv()" class="sticky-btn"
      matTooltip="Status Key" matTooltipPosition="after"><span><i class="fa fa-info-circle"></i></span></button>
    }
    @if (isDivVisible) {
      <div class="toggle-div">
        <button (click)="toggleDiv()" class="close"><span><i class="fa fa-times"></i></span></button>
        <h6 class="status-indicator-head">Legends</h6>
        @if (getLegendsLength()) {
          <ul>
            @for (status of getLegends(); track status) {
              <li
                [ngStyle]="{'background-color': status.backgroundColor,'color':status.fontColor,'border': '2px solid', 'borderColor': status.borderColor}">
              {{status.category}}</li>
            }
          </ul>
        }
      </div>
    }
  </div>