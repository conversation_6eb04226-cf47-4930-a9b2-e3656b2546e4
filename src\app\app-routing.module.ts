import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guard/auth.guard';

const routes: Routes = [
  { path: '', loadChildren: () => import('./components/dashboards/dashboards.module').then(m => m.DashboardsModule) },
  { path: 'jira-timelog',data: { capabilityGroupName: 'App_Page', capabilityName: 'Teams_Dashboard' }, canActivate: [AuthGuard], loadChildren: () => import('./components/jira-timelog/jira-timelog.module').then(m => m.JiraTimelogModule) },
  { path: 'ba-reporting',data: { capabilityGroupName: 'App_Page', capabilityName: 'Teams_Dashboard' }, canActivate: [AuthGuard], loadChildren: () => import('./components/ba-reporting/ba-reporting.module').then(m => m.BaReportingModule) },
  // { path: 'dashboard', loadChildren: () => import('./components/dashboards/dashboards.module').then(m => m.DashboardsModule) },
  { path:'code' , redirectTo:'dashboard' },
  { path: '**', redirectTo: 'daily-dashboard' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes,{ useHash: true })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
