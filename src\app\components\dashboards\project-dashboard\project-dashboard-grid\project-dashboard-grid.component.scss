table {
    border-collapse: collapse;
  }
  
  th, td {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
  }
  
  thead {
    background-color: #f8f9fa;
    font-weight: bold;
  }
  
  tbody tr:hover {
    background-color: #eef6fc;
    cursor: pointer;
  }
  .font-custom{
    font-family: 'Poppins', sans-serif !important;
    font-weight: 400;
    color: #181D1F;
    font-size: 13px;
  }
  .font-custom-td{
    font-family: 'App', sans-serif !important;
    font-family:'apple-system', 'BlinkMacSystemFont';
    font-weight: 400;
    color: #181D1F;
    font-size: 12px;
  }
  .table-custom {
    border-collapse: separate;
    border-spacing: 0 1px;
  }
  .div-height{
    height: calc(100vh - 210px) !important;
  }
 .empty-custom {
   display: flex; 
   justify-content: center; 
   align-items: center;
   height: 100%;
 }
  