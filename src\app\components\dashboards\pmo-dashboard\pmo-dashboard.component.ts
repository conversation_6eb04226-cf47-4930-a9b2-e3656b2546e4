import { Component, OnInit } from '@angular/core';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import { NormalHeaderComponent } from '../../shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { NormalComponent } from '../../shared-components/ag-renderers/cells/normal/normal.component';
import { HourComponent } from '../../shared-components/ag-renderers/cells/hour/hour.component';
import { PercentageComponent } from '../../shared-components/ag-renderers/cells/percentage/percentage.component';
import { JiraIdComponent } from '../../shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { HourInfoComponent } from '../../shared-components/ag-renderers/cells/hour-info/hour-info.component';
import { environment } from 'src/environments/environment';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { DateInfoComponent } from '../../shared-components/ag-renderers/cells/date-info/date-info.component';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import moment from 'moment';
import { LoaderService } from 'loader';
import { SharedService } from 'src/app/service/shared.service';
import { Subscription } from 'rxjs';

@Component({
    selector: 'app-pmo-dashboard',
    templateUrl: './pmo-dashboard.component.html',
    styleUrls: ['./pmo-dashboard.component.scss'],
    standalone: false
})
/*
  It is Parent component, It is a combination of Past due, Missing Due,Missing Estimates and Risk Tickets
*/
export class PmoDashboardComponent implements OnInit {
  gridStyle: string = dashboardConstants.gridStyleAccordian;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  gridOptions = dashboardConstants.gridOptions;
  gridApi: any;
  columnDefs = dashboardConstants.pmoPastDueDateColDef;
  missingDueDateColumnDefs = dashboardConstants.pmoMissingDueDateColDef;
  missingEstimateColumnDefs = dashboardConstants.pmoMissingEstimateColDef;
  riskColumnDefs = dashboardConstants.pmoRiskColDef;

  pastDueDateData: any[] = [];
  riskData: any[] = [];
  missingDueDateData: any[] = [];
  missingEstimateData: any[] = [];
  isPastDateLoader: boolean = true;
  isMissingDueDateLoader: boolean = true;
  isMissingEstimateLoader: boolean = true;
  isRiskLoader: boolean = true;
  assetURL = environment.appBaseURL;
  isDivVisible!: boolean;
  private masterDataSubscription!: Subscription;
  // Component registry for ag-grid v30+
  components = {
    normalHeader: NormalHeaderComponent,
    normalRenderer: NormalComponent,
    hourRenderer: HourComponent,
    percentageRenderer: PercentageComponent,
    jiraIdRenderer: JiraIdComponent,
    hourInfoRenderer: HourInfoComponent,
    dateInfoRenderer: DateInfoComponent
  };

  constructor(private onePmService: OnepmServiceService, private notification: NzNotificationService, private loaderService: LoaderService, private sharedService: SharedService) {}

  ngOnInit(): void {
    this.getPMOInfo('PastDueDate');
    this.getPMOInfo('MissingDueDates');
    this.getPMOInfo('MissingEstimates');
    this.getPMOInfo('AtRisk');
    this.getMasterData();
    this.loaderService.invokeLoaderComponent(false);
  }
  /**
   * Triggered when the grid is ready.
   * @param params - Grid API parameters
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    // Note: setHeaderHeight is deprecated in AG Grid v33+, use [headerHeight] in template instead
  }
  /**
   * Method to call the API to get the PMO dashboard data.
   * @param params 
   */
  getPMOInfo(params: string) {
    this.onePmService.getPMODashboardInfo(params).subscribe((res: any) => {
      if (res.succeeded) {
        switch (params) {
          case 'PastDueDate':
            this.pastDueDateData = res.data;
            this.isPastDateLoader = false;
            break;
          case 'MissingDueDates':
            this.missingDueDateData = res.data;
            this.isMissingDueDateLoader = false;
            break;
          case 'MissingEstimates':
            this.missingEstimateData = res.data;
            this.isMissingEstimateLoader = false;
            break;
          case 'AtRisk':
            this.riskData = res.data;
            this.isRiskLoader = false;
            break;
        }
      }
      else {
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
  /**
   * Method to highlight the cell while export excel
   * @param cell 
   */
  onTitleCellHighlight(cell: any) {
    cell.alignment = { vertical: 'middle' };
    cell.font = { bold: true, color: { argb: 'FFFFFF' } }
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '000000' },
    };
  }
  /**
   * Method format header cell while export excel
   * @param cell 
   */
  onHeaderCellHighlight(cell: any) {
    cell.font = { bold: true };
    cell.alignment = { vertical: 'middle', horizontal: 'center' };
  }
  /**
   * Method to export the PMO dashboard data to excel
   */
  onExportPMO() {
    this.loaderService.invokeLoaderComponent(true);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('PMO Dashboard');
    const title = 'PMO_Dashboard_List' + (new Date().getMonth() + 1) + '_' + new Date().getDate() + '_' + new Date().getFullYear();
    const rowNumber = this.pastDueDateData.length + 4;
    const rowEstimateNumber = this.pastDueDateData.length + this.missingDueDateData.length + 7;
    const rowRiskNumber = this.pastDueDateData.length + this.missingDueDateData.length + this.missingEstimateData.length + 10;
    // past due tickets starts here
    worksheet.addRow(['Past Due Tickets']);
    this.onTitleCellHighlight(worksheet.getCell(1, 1));
    worksheet.mergeCells(1, 1, 1, 6);
    let headers: any = [];
    this.columnDefs.forEach(ele => {
      if (ele.headerName != '') headers.push(ele.headerName);
    });
    worksheet.addRow(headers);
    const headerRow = worksheet.getRow(2);
    headerRow.eachCell((cell, colNumber) => {
      this.onHeaderCellHighlight(cell);
    });
    this.pastDueDateData.forEach((data) => {
      const row = worksheet.addRow([
        data.project,
        data.issueKey,
        data.summary,
        data.assignee,
        data.developmentStage,
        data.dueDateInfo.date ? this.displayExportDateFormats(data.dueDateInfo.date) : '',
      ]);
      // Style the Due Date Column (Column 5)
      const dueDateCell = row.getCell(6);
      this.onHighlightCell(dueDateCell, data.dueDateInfo);
    });

    // past due tickets ends here
    //-- Missing Due dates starts here
    worksheet.addRow([]);
    worksheet.addRow(['Missing Due Dates']);
    this.onTitleCellHighlight(worksheet.getCell(rowNumber, 1));
    worksheet.mergeCells(rowNumber, 1, rowNumber, 5);
    let missingHeader: any = [];
    this.missingDueDateColumnDefs.forEach(ele => {
      if (ele.headerName != '') missingHeader.push(ele.headerName);
    });
    worksheet.addRow(missingHeader);
    const secondHeaderRow = worksheet.getRow(rowNumber + 1);
    secondHeaderRow.eachCell((cell, colNumber) => {
      this.onHeaderCellHighlight(cell);
    });

    this.missingDueDateData.forEach((data) => {
      worksheet.addRow([
        data.project,
        data.issueKey,
        data.summary,
        data.assignee,
        data.developmentStage,
      ]);
    });
    //-- Missing Due dates ends here
    //-- Missing Estimates starts here
    worksheet.addRow([]);
    worksheet.addRow(['Missing Estimates']);
    this.onTitleCellHighlight(worksheet.getCell(rowEstimateNumber, 1));
    worksheet.mergeCells(rowEstimateNumber, 1, rowEstimateNumber, 5);
    let estimateHeader: any = [];
    this.missingEstimateColumnDefs.forEach(ele => {
      if (ele.headerName != '') estimateHeader.push(ele.headerName);
    });
    worksheet.addRow(estimateHeader);

    const thirdHeaderRow = worksheet.getRow(rowEstimateNumber + 1);
    thirdHeaderRow.eachCell((cell, colNumber) => {
      this.onHeaderCellHighlight(cell);
    });
    this.missingEstimateData.forEach((data) => {
      worksheet.addRow([
        data.project,
        data.issueKey,
        data.summary,
        data.assignee,
        data.developmentStage,
      ]);
    });
    //-- Missing Estimates ends here

    worksheet.addRow([]);
    worksheet.addRow(['Risk Tickets']);
    this.onTitleCellHighlight(worksheet.getCell(rowRiskNumber, 1));
    worksheet.mergeCells(rowRiskNumber, 1, rowRiskNumber, 10);
    let riskHeader: any = [];
    this.riskColumnDefs.forEach(ele => {
      if (ele.headerName != '') riskHeader.push(ele.headerName);
    });
    worksheet.addRow(riskHeader);

    const fourthHeaderRow = worksheet.getRow(rowRiskNumber + 1);
    fourthHeaderRow.eachCell((cell, colNumber) => {
      this.onHeaderCellHighlight(cell);
    });
    this.riskData.forEach((data) => {
      const row = worksheet.addRow([
        data.project,
        data.issueKey,
        data.summary,
        data.assignee,
        data.developmentStage,
        data.dueDateInfo.date ? this.displayExportDateFormats(data.dueDateInfo.date) : '',
        data.estimate,
        data?.workLogInfo.hours,
        data?.remainingEffortInfo?.hours,
        data?.remainingTimeInfo?.hours,
      ]);
      const dueDateCell = row.getCell(6);
      const spentCell = row.getCell(8);
      const effortCell = row.getCell(9);
      const timeCell = row.getCell(10);
      this.onHighlightCell(dueDateCell, data.dueDateInfo);
      this.onHighlightCell(spentCell, data.workLogInfo);
      this.onHighlightCell(effortCell, data.remainingEffortInfo);
      this.onHighlightCell(timeCell, data.remainingTimeInfo);
    });
    // Adjust Column Widths
    worksheet.columns.forEach((column) => {
      worksheet.getColumn(3).width = 50;
      column.width = 30;
    });
    workbook.xlsx.writeBuffer().then((data) => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, title + '.xlsx');
    });
    this.loaderService.invokeLoaderComponent(false);
  }
  /**
   * Method to highlight the cell while exporting excel
   * @param cell 
   * @param data 
   */
  onHighlightCell(cell: any, data: any) {
    if (data.backgroundColor) {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: data.backgroundColor.replace('#', '') },
      };
    }
    if (data.fontColor) {
      cell.font = { color: { argb: data.fontColor.replace('#', '') } };
    }
    if (data.borderColor) {
      cell.border = {
        top: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        left: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        bottom: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
        right: { style: 'thin', color: { argb: data.borderColor.replace('#', '') } },
      };
    }
  }
  /**
   * Method to change the date format while exporting excel
   * @param value 
   * @returns 
   */
  displayExportDateFormats(value: Date) {
    return moment(value).format('MM-DD-YYYY')
  }
  /**
   * Method to toggle legends
   */
  toggleDiv() {
    this.isDivVisible = !this.isDivVisible;
  }
  /**
   * Method to get legends length from shared service
   * @returns 
   */
  getLegendsLength() {
    return this.sharedService?.legends.length > 0;
    /**
     * Method to get legends from shared service
     */
  }
  getLegends() {
    return this.sharedService?.legends;
  }
  /**
   * Method to get master data by calling API
   * @returns 
   */
  getMasterData() {
    if (this.sharedService.legends.length > 0) {
      return;
    }
    this.masterDataSubscription = this.onePmService.getMasterData().subscribe((res: any) => {
      if (res.succeeded) {
        this.loaderService.invokeLoaderComponent(false);
        this.sharedService.legends = res?.data?.legendsInfo;
      }
      else {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
}
