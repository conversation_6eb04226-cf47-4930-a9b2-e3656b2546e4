<table class="table table-striped table-custom">
  <thead>
    <tr>
      <th scope="col" class="font-custom" style="width: 150px;">Key</th>
      <th scope="col" class="font-custom" style="width: 350px;">Description</th>
      <th scope="col" class="font-custom" style="width: 200px;">Assignee</th>
      <th scope="col" class="font-custom" style="width: 120px;">Start Date</th>
      <th scope="col" class="font-custom" style="width: 120px;">Due Date</th>
      <th scope="col" class="font-custom" style="width: 190px;">Status</th>
      <th scope="col" class="font-custom text-center" style="width: 100px;">#Hrs</th>
      <th scope="col" class="font-custom text-center" style="width: 120px;">Remaining</th>
      <th scope="col" class="font-custom text-center" style="width: 150px;">% Hrs Complete</th>
    </tr>
  </thead>
  <tbody>
    @if (rowData.length === 0 && projectFlag) {
      <tr>
        <td colspan="9" class="text-center div-height">
          <div class="empty-custom">
            <span class="text-muted font-custom-td">No records found</span>
          </div>
        </td>
      </tr>
    }

    <!-- Top-level (Layer 1) -->
    @for (parent of getTopLevelRows(); track parent) {
      <ng-container *ngTemplateOutlet="renderRow; context: { row: parent, level: 0 }"></ng-container>
      <!-- Layer 2 (Children of top level) -->
      @if (isExpanded(parent.issueKey)) {
        @for (child of getChildren(parent.issueKey); track child) {
          <ng-container *ngTemplateOutlet="renderRow; context: { row: child, level: 1 }"></ng-container>
          <!-- Layer 3 (Grandchildren of top level) -->
          @if (isExpanded(child.issueKey)) {
            @for (grandchild of getChildren(child.issueKey); track grandchild) {
              <ng-container *ngTemplateOutlet="renderRow; context: { row: grandchild, level: 2 }"></ng-container>
            }
          }
        }
      }
    }
  </tbody>
</table>

<ng-template #renderRow let-row="row" let-level="level">
  <tr style="cursor: pointer;">
    <td [style.padding-left.px]="level * 20" class="font-custom-td" (click)="toggleRow(row.issueKey)">
      @if (getChildren(row.issueKey).length > 0) {
        <span>
          <i [class]="isExpanded(row.issueKey) ? 'fa fa-chevron-down mx-1' : 'fa fa-chevron-right mx-1'" style="opacity: 0.7;font-size: 10px;"></i>
          @if (row.issueKeyType === 'Epic') {
            <img [src]="assetURL + '/assets/images/epic.svg'" class="mx-1" height="12px"
              title="Epic">
          }
        </span>
      }
      @if (row.issueKeyType === 'Story') {
        <i class="fa fa-bookmark-o text-success mx-1" aria-hidden="true"
        title="Story"></i>
      }
      @if (row.issueKeyType ==='Task' || row.issueKeyType ==='Sub-task') {
        <img [src]="assetURL + '/assets/images/task.svg'" class="mx-1" height="12px"
          title="Task">
      }
      @if (row.issueKeyType ==='Bug/Issue' || row.issueKeyType ==='Bug') {
        <img [src]="assetURL + '/assets/images/bug.svg'" class="mx-1" height="12px"
          title="Bug">
      }
      <a href="{{jiraUrl + row.issueKey}}" target="_blank" class="link" id="link-{{row.issueKey}}">{{row.issueKey}} </a>
    </td>
    <td class="font-custom-td">{{ row.description }}</td>
    <td class="font-custom-td">
      {{ row.issueKeyType === 'Epic' ? '' : row.assignee }}
    </td>
    <td class="font-custom-td">{{ row.startDate | date: 'MM-dd-yyyy' }}</td>
    <td class="font-custom-td" [ngStyle]="{
            'background-color': row.dueDateInfo.backgroundColor,
            'color': row.dueDateInfo.fontColor,
            'border': row.dueDateInfo.borderColor ? '2px solid ' + row.dueDateInfo.borderColor : ''
          }">{{ row.issueKeyType === 'Epic' ? '' : row.dueDateInfo.date | date: 'MM-dd-yyyy' }}
    </td>
    <td class="font-custom-td">{{ row.issueKeyType === 'Epic' ? getEpicStatus(row.issueKey) : row.developmentStage }}</td>
    <td class="text-center font-custom-td" [ngStyle]="{
            'background-color': row.workLogInfo.backgroundColor,
            'color': row.workLogInfo.fontColor,
            'border': row.workLogInfo.borderColor ? '2px solid ' + row.workLogInfo.borderColor : ''
          }">
      <!-- {{ row.workLogInfo.hours  }}  -->
      {{ getTotalHours(row.issueKey) | number:'1.0-2'}}
    </td>
    <td class="text-center font-custom-td">{{ getTotalRemainingHours(row.issueKey) | number:'1.0-2'}}</td>
    <td class="text-center font-custom-td">
      {{ getPercentageHoursComplete(row.issueKey) }}
    </td>
    <!-- <td class="text-center font-custom-td">{{ row.remainingHours }}</td> -->
    <!-- <td class="text-center font-custom-td">{{ row.remainingHoursPercentage }}%</td> -->
  </tr>
</ng-template>