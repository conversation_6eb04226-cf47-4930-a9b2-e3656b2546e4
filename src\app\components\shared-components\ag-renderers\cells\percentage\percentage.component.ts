import { Component } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
    selector: 'app-percentage',
    templateUrl: './percentage.component.html',
    styleUrls: ['./percentage.component.scss'],
    standalone: false
})
export class PercentageComponent  implements AgRendererComponent{

  constructor() { }

  
    cellValue:any;
    agInit(params: ICellRendererParams): void { 
      this.cellValue = params.value;
     }
    refresh(params: ICellRendererParams):boolean {
      this.cellValue =params.value;
      return true
    }

    formatNumber(value: any): string {      
      if (value == null) {
        return '';
      }
      // Check if the value is an integer or has non-zero decimal places
      const numericValue = parseFloat(value); // Ensure it's parsed as a number
      const hasDecimals = numericValue % 1 !== 0;
      return hasDecimals ? numericValue.toFixed(2) : numericValue.toFixed(0);
    }
    

}
