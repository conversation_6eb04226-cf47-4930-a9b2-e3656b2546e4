import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { Observable, lastValueFrom } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { AuthService } from '../service/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard  {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree  {
    return this.authService.capabilitiesSubject.toPromise().then(value => {
      const permissionCheck = this.checkPermission(route);
      return typeof permissionCheck === 'boolean'
        ? permissionCheck
        : this.router.parseUrl('/dashboard/unauthorized');
      
    })
  }

  private checkPermission(route: ActivatedRouteSnapshot): boolean  {
    const capabilityName = route.data['capabilityName'];
    const capabilityGroup = route.data['capabilityGroupName'];
    const hasAccess = this.authService.haveViewAccess(
      capabilityGroup,
      capabilityName
    );

    if (hasAccess) {
      return true;
    } else {
      this.router.navigate(['/unauthorized']);
      return false;
    }
  }
}