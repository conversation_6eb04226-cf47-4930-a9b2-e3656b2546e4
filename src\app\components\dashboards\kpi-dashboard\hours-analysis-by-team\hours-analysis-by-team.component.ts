import { Component, Input, OnInit } from '@angular/core';
import { DateInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/date-info/date-info.component';
import { DateComponent } from 'src/app/components/shared-components/ag-renderers/cells/date/date.component';
import { DescriptionComponent } from 'src/app/components/shared-components/ag-renderers/cells/description/description.component';
import { HourInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour-info/hour-info.component';
import { HourComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour/hour.component';
import { JiraIdComponent } from 'src/app/components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { NormalComponent } from 'src/app/components/shared-components/ag-renderers/cells/normal/normal.component';
import { PercentageComponent } from 'src/app/components/shared-components/ag-renderers/cells/percentage/percentage.component';
import { NormalHeaderComponent } from 'src/app/components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { KpiExportService } from 'src/app/service/kpi-export.service';
import { dashboardConstants } from 'src/app/utils/apps-constants';

@Component({
    selector: 'app-hours-analysis-by-team',
    templateUrl: './hours-analysis-by-team.component.html',
    styleUrls: ['./hours-analysis-by-team.component.scss'],
    standalone: false
})
/* 
  Hours Analysis by Team Dashboard,child of KPI Dashboard
*/
export class HoursAnalysisByTeamComponent implements OnInit {

  
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  columnDefs: any[] = dashboardConstants.kpiHoursAnalysisByTeam;
  teamSummaryColumnDefs: any[] = dashboardConstants.teamSummaryColDef;
  growthSummarydColumnDefs: any[] = dashboardConstants.growthSummaryColDef;
  jiraSummaryColumnDefs: any[] = dashboardConstants.jiraSummaryColDef;
  @Input() teamSummaryGridData: any[] = [];
  @Input() growthSummaryGridData: any[] = [];
  @Input() jiraSummaryGridData: any[] = [];
  @Input() gridData: any[] = [];
  @Input() startDate: string = '';
  @Input() endDate: string = '';
  teamSummarypinnedBottom = [];
  jiraGridPinnedBottom: any[] = [];
  growthSummaryPinnedBottom: any[] = [];
  teamSummaryPinnedBottom: any[] = [];
  pinnedBottomRowData: any[] = [];

  jiraTotal: number = 0;
  growthSummaryTotal: number = 0;
  teamSummaryTotal: number = 0;
  totalPlannedHours: number = 0;
  totalUnplannedHours: number = 0;
  totalTotalHours: number = 0;

  secondDefaultColDef = {
    flex: 1,
    minWidth: 90,
    sortable: false,
    resizable: false,
  };
  
  gridApi: any;
  gridStyle: string = 'height: calc(100vh - 380px) !important; width: 100%;';
  gridOptions = dashboardConstants.gridOptions;
  // Component registry for ag-grid v30+
  components = {
    normalHeader: NormalHeaderComponent,
    normalRenderer: NormalComponent,
    hourRenderer: HourComponent,
    descRenderer: DescriptionComponent,
    dateRenderer: DateComponent,
    percentageRenderer: PercentageComponent,
    jiraIdRenderer: JiraIdComponent,
    dateInfoRenderer: DateInfoComponent,
    hourInfoRenderer: HourInfoComponent,
  };

  constructor(private exportService: KpiExportService) {}

  ngOnInit(): void {
  }

  ngOnChanges() {
    if (this.jiraSummaryGridData.length > 0) {
      this.jiraTotal = this.jiraSummaryGridData.reduce((sum, item) => sum + item.jiraTicketCount, 0);
      this.jiraGridPinnedBottom = [{ team: 'Total', jiraTicketCount: this.jiraTotal },];
    }
    if (this.growthSummaryGridData.length > 0) {
      this.growthSummaryTotal = this.growthSummaryGridData.reduce((sum, item) => sum + item.totalHours, 0);
      if(Number(this.growthSummaryTotal)) this.growthSummaryPinnedBottom = [{ transformPercentage: 'Total', totalHours:Math.round(this.growthSummaryTotal * 100) / 100  }];
    } if (this.teamSummaryGridData.length > 0) {
      this.teamSummaryTotal = this.teamSummaryGridData.reduce((sum, item) => sum + item.totalHours, 0);
      this.teamSummaryPinnedBottom = [{ unPlannedPercentage: 'Total', totalHours:Math.round(this.teamSummaryTotal * 100) / 100 }];
    }
    if (this.gridData.length > 0) {
      this.columnDefs = dashboardConstants.kpiHoursAnalysisByTeam.map((col, index) => {
        if (index === 0) {  // Updating only the first column header
          this.exportService.hourAnalysisGridHeader = `Planned/Unplanned and RUN/GROW/TRANSFORM hours for BA, App Dev and Data Teams for ${this.startDate} to ${this.endDate}`;
          return {
            ...col,
            headerName: `Planned/Unplanned and RUN/GROW/TRANSFORM hours for BA, App Dev and Data Teams for ${this.startDate} to ${this.endDate}`
          };
        }
        return col;  // Keep other columns unchanged
      });
      this.totalPlannedHours = this.gridData.reduce((sum, item) => sum + item.totalHours.totalPlanned, 0);
      this.totalUnplannedHours = this.gridData.reduce((sum, item) => sum + item.totalHours.totalUnPlanned, 0);
      this.totalTotalHours = this.gridData.reduce((sum, item) => sum + item.totalHours.totalHours, 0);
      this.pinnedBottomRowData = [
        {
          projectName: "", planned: { ba: '', appDev: '', data: '', rpa: '' },
          unPlanned: { ba: '', appDev: '', data: '', rpa: 'Total' },
          totalHours: { totalPlanned: this.totalPlannedHours, totalUnPlanned: this.totalUnplannedHours, totalHours: this.totalTotalHours }
        }
      ];
    } else {
      this.pinnedBottomRowData = [];
    }
  }
  /**
   * AG-Grid initialization when the grid is ready.
   * @param params 
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    this.updateColumnSize(this.gridApi);
  }
  /**
   * AG-Grid initialization when the grid is ready.
   * @param params 
   */
  onGridReady2(params: any) {
    params.api.sizeColumnsToFit();
    params.api.autoSizeAllColumns();
    this.updateColumnSize(params.api);
    params.api.sizeColumnsToFit();
  }
  /**
   * Hides the AG-Grid overlay after a short delay.
   */
  showBlankOverlay() {
    setTimeout(() => {
      this.gridApi?.hideOverlay();
    }, 1);
  }
  /**
   * Adjusts column sizes dynamically when the window resizes.
   * @param param 
   */
  updateColumnSize(param: any) { window.addEventListener('resize', () => { setTimeout(() => param.sizeColumnsToFit(), 500) }) }

}
