{"version": 3, "file": "hash-loader.umd.js", "sources": ["../../../projects/hash-loader/src/lib/loader.service.ts", "../../../projects/hash-loader/src/lib/hash-loader.component.ts", "../../../projects/hash-loader/src/lib/hash-loader.component.html", "../../../projects/hash-loader/src/lib/hash-loader.module.ts", "../../../projects/hash-loader/src/public-api.ts", "../../../projects/hash-loader/src/hash-loader.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class LoaderService {\n  isLoading = new Subject<boolean>();\n\n  invokeLoaderComponent(status:boolean){\n    this.isLoading.next(status);\n  }\n}\n", "import { Component, Input, OnInit } from '@angular/core';\nimport { LoaderService } from './loader.service';\n\n@Component({\n  selector: 'lib-hash-loader',\n  templateUrl: './hash-loader.component.html',\n  styleUrls: ['./hash-loader.component.scss']\n})\nexport class HashLoaderComponent implements OnInit {\n\n  isLoading:boolean = false;\n  @Input() topOffSet:any;\n\n  heightStyle :any;\n\n  constructor(private loaderService: LoaderService) { }\n\n  ngOnInit(): void {\n    if(this.topOffSet) this.heightStyle = `height: calc(100vh - ${this.topOffSet})`;\n    else this.heightStyle = 'height: 100vh';\n    this.loaderService.isLoading.subscribe((status:boolean) => {\n      this.isLoading = status;\n    });\n  }\n\n}\n", "<div *ngIf=\"isLoading && heightStyle\" id=\"pause\" class=\"d-flex align-items-center justify-content-center\" [attr.style]=\"heightStyle\">\r\n      <div class=\"hash-loader\"></div>\r\n</div>\r\n", "import { NgModule } from '@angular/core';\nimport { HashLoaderComponent } from './hash-loader.component';\nimport { BrowserModule } from '@angular/platform-browser';\n\n\n\n@NgModule({\n  declarations: [\n    HashLoaderComponent\n  ],\n  imports: [\n    BrowserModule\n  ],\n  exports: [\n    HashLoaderComponent\n  ]\n})\nexport class HashLoaderModule { }\n", "/*\n * Public API Surface of hash-loader\n */\n\nexport * from './lib/loader.service';\nexport * from './lib/hash-loader.component';\nexport * from './lib/hash-loader.module';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": ["Subject", "i0", "Injectable", "i1.LoaderService", "i2", "Component", "Input", "BrowserModule", "NgModule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,QAAA,aAAA,kBAAA,YAAA;IAHA,IAAA,SAAA,aAAA,GAAA;IAIE,QAAA,IAAA,CAAA,SAAS,GAAG,IAAIA,YAAO,EAAW,CAAC;SAKpC;QAHC,aAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,MAAc,EAAA;IAClC,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7B,CAAA;;;oIALU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAAC,aAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;IAAb,aAAA,CAAA,KAAA,GAAAA,aAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAAA,aAAA,EAAA,IAAA,EAAA,aAAa,cAFZ,MAAM,EAAA,CAAA,CAAA;qHAEP,aAAa,EAAA,UAAA,EAAA,CAAA;sBAHzBC,aAAU;IAAC,YAAA,IAAA,EAAA,CAAA;IACV,oBAAA,UAAU,EAAE,MAAM;qBACnB,CAAA;;;ACGD,QAAA,mBAAA,kBAAA,YAAA;IAOE,IAAA,SAAA,mBAAA,CAAoB,aAA4B,EAAA;IAA5B,QAAA,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;IALhD,QAAA,IAAS,CAAA,SAAA,GAAW,KAAK,CAAC;SAK2B;IAErD,IAAA,mBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;YAAA,IAMC,KAAA,GAAA,IAAA,CAAA;YALC,IAAG,IAAI,CAAC,SAAS;gBAAE,IAAI,CAAC,WAAW,GAAG,uBAAA,GAAwB,IAAI,CAAC,SAAS,MAAG,CAAC;;IAC3E,YAAA,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;YACxC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,UAAC,MAAc,EAAA;IACpD,YAAA,KAAI,CAAC,SAAS,GAAG,MAAM,CAAC;IAC1B,SAAC,CAAC,CAAC;SACJ,CAAA;;;0IAfU,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,aAAA,EAAA,CAAA,EAAA,MAAA,EAAAF,aAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;IAAnB,mBAAA,CAAA,IAAA,GAAAA,aAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,sGCRhC,wMAGA,EAAA,MAAA,EAAA,CAAA,gxCAAA,CAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAAG,aAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;qHDKa,mBAAmB,EAAA,UAAA,EAAA,CAAA;sBAL/BC,YAAS;IAAC,YAAA,IAAA,EAAA,CAAA;IACT,oBAAA,QAAQ,EAAE,iBAAiB;IAC3B,oBAAA,WAAW,EAAE,8BAA8B;wBAC3C,SAAS,EAAE,CAAC,8BAA8B,CAAC;qBAC5C,CAAA;qGAIU,SAAS,EAAA,CAAA;0BAAjBC,QAAK;;;AEMR,QAAA,gBAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,gBAAA,GAAA;;;;uIAAa,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAAL,aAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;IAAhB,gBAAA,CAAA,IAAA,GAAAA,aAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAAA,aAAA,EAAA,IAAA,EAAA,gBAAgB,EATzB,YAAA,EAAA,CAAA,mBAAmB,CAGnB,EAAA,OAAA,EAAA,CAAAM,6BAAa,aAGb,mBAAmB,CAAA,EAAA,CAAA,CAAA;IAGV,gBAAA,CAAA,IAAA,GAAAN,aAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAAA,aAAA,EAAA,IAAA,EAAA,gBAAgB,EAPlB,OAAA,EAAA,CAAA;gBACPM,6BAAa;aACd,CAAA,EAAA,CAAA,CAAA;qHAKU,gBAAgB,EAAA,UAAA,EAAA,CAAA;sBAX5BC,WAAQ;IAAC,YAAA,IAAA,EAAA,CAAA;IACR,oBAAA,YAAY,EAAE;4BACZ,mBAAmB;IACpB,qBAAA;IACD,oBAAA,OAAO,EAAE;4BACPD,6BAAa;IACd,qBAAA;IACD,oBAAA,OAAO,EAAE;4BACP,mBAAmB;IACpB,qBAAA;qBACF,CAAA;;;IChBD;;IAEG;;ICFH;;IAEG;;;;;;;;;;;;"}