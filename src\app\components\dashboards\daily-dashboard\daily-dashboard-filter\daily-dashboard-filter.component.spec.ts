import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DailyDashboardFilterComponent } from './daily-dashboard-filter.component';

describe('DailyDashboardFilterComponent', () => {
  let component: DailyDashboardFilterComponent;
  let fixture: ComponentFixture<DailyDashboardFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DailyDashboardFilterComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DailyDashboardFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
