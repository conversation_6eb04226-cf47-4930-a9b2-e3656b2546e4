import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BaReportingRoutingModule } from './ba-reporting-routing.module';
import { PmoEODReportingComponent } from './ba-reporting-dashboard/pmo-eodreporting/pmo-eodreporting.component';
import { BaReportingFilterComponent } from './ba-reporting-dashboard/ba-reporting-filter/ba-reporting-filter.component';
import { BaReportingDashboardComponent } from './ba-reporting-dashboard/ba-reporting-dashboard.component';
import { AgGridModule } from 'ag-grid-angular';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { EodReportingComponent } from './ba-reporting-dashboard/eod-reporting/eod-reporting.component';
import { CommonEODReportComponent } from './ba-reporting-dashboard/eod-reporting/common-eodreport/common-eodreport.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';


@NgModule({
  declarations: [
    PmoEODReportingComponent,
    BaReportingFilterComponent,
    BaReportingDashboardComponent,
    EodReportingComponent,
    CommonEODReportComponent
  ],
  imports: [
    CommonModule,
    BaReportingRoutingModule,
    AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    NzButtonModule,
    NzDatePickerModule,
    NzCollapseModule,
    NzSpinModule,
  ],
  providers: [
    NzNotificationService
  ]
})
export class BaReportingModule { }
