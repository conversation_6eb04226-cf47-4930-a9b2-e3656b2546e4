::ng-deep {
  .dropdown-list li h5 {
    font-size: 12px !important;
  }
}

.custom-input {
  border: 1px solid #ced4da !important;
  border-radius: .375rem !important;
  font-size: 12px !important;
}

.custom-input:hover {
  border-color: #40a9ff !important;
  border-right-width: 1px !important;
}

.ant-picker-focused {
  outline: 0;
  box-shadow: 0 0 0 .25rem #0d6efd40 !important;
}

.date-input {
  .ant-picker {
    padding: 2px 11px !important;
  }
}

.multi-select-container {
  margin-top: 25px !important;
}

.date-height {
  height: 36px !important;
}

::ng-deep .multi-select-container .dropdown-list ul.item2 {
  position: absolute;
  background: #fff;
  width: 100%;
  left: 0;
  box-shadow: 0 1px 5px #959595;
}

.btn-custom {
  background-color: #538ABC !important;

}

.ant-btn-primary[disabled] {
  background-color: #40a9ff !important;
  color: #fff;
}

.custom-dropdown {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}