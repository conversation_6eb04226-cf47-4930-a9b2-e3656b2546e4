<ag-grid-angular #agGrid1 [style]="gridStyle" class="ag-theme-alpine business-application-grid"
    [columnDefs]="columnDefs" [components]="components" [defaultColDef]="defaultColDef" [rowData]="rowData" [suppressDragLeaveHidesColumns]="true"
    [overlayNoRowsTemplate]="noRowsTemplate" (gridReady)="onGridReady($event)" [rowHeight]="20" [gridOptions]="gridOptions">
</ag-grid-angular>
<div class="sticky-buttons-group" *ngIf="chartData">
    <button nz-button nzType="primary" *ngIf="!isDivVisible" (click)="toggleDiv()" class="sticky-btn"
        matTooltip="Status Key" matTooltipPosition="after"><span><i class="fa fa-bar-chart"
                aria-hidden="true"></i></span></button>
    <div class="toggle-div" *ngIf="isDivVisible">
        <button (click)="toggleDiv()" class="close"><span><i class="fa fa-times"></i></span></button>
        <highcharts-chart [Highcharts]="Highcharts" [options]="chartOptions"
            style="width: 100%; height: 500px; display: block;">
        </highcharts-chart>

    </div>
</div>