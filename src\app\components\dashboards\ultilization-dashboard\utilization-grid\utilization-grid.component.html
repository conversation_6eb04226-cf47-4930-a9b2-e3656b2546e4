<ag-grid-angular #agGrid1 [style]="gridStyle" class="ag-theme-alpine business-application-grid"
  [columnDefs]="columnDefs" [components]="components" [defaultColDef]="defaultColDef" [rowData]="rowData" [suppressDragLeaveHidesColumns]="true"
  [overlayNoRowsTemplate]="noRowsTemplate" (gridReady)="onGridReady($event)" [rowHeight]="20" [gridOptions]="gridOptions">
</ag-grid-angular>
@if (chartData) {
  <div class="sticky-buttons-group">
    @if (!isDivVisible) {
      <button nz-button nzType="primary" (click)="toggleDiv()" class="sticky-btn"
        matTooltip="Status Key" matTooltipPosition="after"><span><i class="fa fa-bar-chart"
      aria-hidden="true"></i></span></button>
    }
    @if (isDivVisible) {
      <div class="toggle-div">
        <button (click)="toggleDiv()" class="close"><span><i class="fa fa-times"></i></span></button>
        <highcharts-chart [Highcharts]="Highcharts" [options]="chartOptions"
          style="width: 100%; height: 500px; display: block;">
        </highcharts-chart>
      </div>
    }
  </div>
}