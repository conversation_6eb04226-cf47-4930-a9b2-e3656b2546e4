<header class="navbar navbar-expand-lg sticky-top" id="top-navbar">
  <div class="container-fluid ps-2">
    <a (click)="gotoHome('App_Page','Daily_Status_Report')"
      class="d-flex align-items-center mb-2 mb-lg-0 text-dark text-decoration-none">
      <img id="main-logo" src="{{headerConstant.logo_url}}">
      <span id="app-name"><img id="app-logo" [src]="headerConstant.app_logo"></span>
    </a>
    @if (isLoggedIn()) {
      <div class="dropdown text-end d-flex">
        @if (haveOnePmMenuAccess('App_Page','Daily_Status_Report')) {
          <div class="top-nav-menu-container">
            <button id="app-switching-button"
              [attr.class]="iconType === 'daily' ? 'top-nav-menu-btn btn-active me-2' : 'top-nav-menu-btn btn-inactive me-2'"
              [routerLink]="['/daily-dashboard']" (click)="onClickIcon('daily')" title="Daily Dashboard">
              <img src="{{iconType === 'daily' ? headerConstant.onePM_daily_icon: headerConstant.onePM_daily_icon_white}}"
                style="margin-right: 5px;">
              </button>
            </div>
          }
          @if (haveOnePmMenuAccess('App_Page','Project_Level_Report')) {
            <div class="top-nav-menu-container">
              <button id="app-switching-button"
                [attr.class]="iconType === 'project' ? 'top-nav-menu-btn btn-active' : 'top-nav-menu-btn btn-inactive'"
                [routerLink]="['/project-dashboard']" (click)="onClickIcon('project')" title="Project Dashboard">
                <img src="{{iconType === 'project' ? headerConstant.onePM_project_icon: headerConstant.onePM_project_icon_white}}"
                  style="margin-right: 5px;">
                </button>
              </div>
            }
            @if (checkMenuAccess()) {
              <div class="top-nav-menu-container apps-menu">
                <button id="onePMMenu"
                  class="dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="true"
                  [attr.class]="isBoardType ? 'top-nav-menu-btn btn-active' : 'top-nav-menu-btn btn-inactive'"
                  aria-expanded="false"
                  (click)="onClickBoardIcon()" title="Board">
                  <i class="fa fa-bar-chart ico-align" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="onePMMenu" [ngClass]="{'show': isMenuOpen}">
                  @if (haveOnePmMenuAccess('App_Page','PMO_Dashboard')) {
                    <a class="dropdown-item" (click)="onClickIcon('pmo')" [ngClass]="iconType === 'pmo' ? 'active' : ''"  [routerLink]="['/pmo-dashboard']">PMO</a>
                  }
                  @if (haveOnePmMenuAccess('App_Page','KPI_Dashboard')) {
                    <a class="dropdown-item" (click)="onClickIcon('kpi')" [ngClass]="iconType === 'kpi' ? 'active' : ''"  [routerLink]="['/kpi-dashboard']">KPI</a>
                  }
                  @if (haveOnePmMenuAccess('App_Page','Teams_Dashboard')) {
                    <a class="dropdown-item" (click)="onClickIcon('teams')" [ngClass]="iconType === 'teams' ? 'active' : ''" [routerLink]="['/teams-dashboard']">Teams</a>
                  }
                  @if (haveOnePmMenuAccess('App_Page','Utilization_Dashboard')) {
                    <a class="dropdown-item" (click)="onClickIcon('utilization')" [ngClass]="iconType === 'utilization' ? 'active' : ''"  [routerLink]="['/utilization-dashboard']">Utilization</a>
                  }
                  @if (haveOnePmMenuAccess('App_Page','Jira_Timelog_Report')) {
                    <a class="dropdown-item" (click)="onClickIcon('jiratimelog')" [ngClass]="iconType === 'jiratimelog' ? 'active' : ''"  [routerLink]="['/jira-timelog']">Jira Timelog</a>
                  }
                  @if (haveOnePmMenuAccess('App_Page','BA_Reporting_Dashboard')) {
                    <a class="dropdown-item" (click)="onClickIcon('baReporting')" [ngClass]="iconType === 'baReporting' ? 'active' : ''"  [routerLink]="['/ba-reporting']">Reports</a>
                  }
                </ul>
              </div>
            }
            @if (checkManagementMenuAccess()) {
              <div class="top-nav-menu-container apps-menu">
                <button id="onePMMenu"
                  class="dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="true"
                  [attr.class]="isManagementType ? 'top-nav-menu-btn btn-active' : 'top-nav-menu-btn btn-inactive'"
                  aria-expanded="false"
                  (click)="onClickManagementIcon()" title="Dashboard">
                  <img src="{{iconType == 'changeManagement' || iconType =='executive' ? headerConstant.onePM_management_icon: headerConstant.onePM_management_icon_white}}"
                    style="margin-right: 5px;">
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="onePMMenu" [ngClass]="{'show': isMenuManagementOpen}">
                    @if (haveOnePmMenuAccess('App_Page','Change_Management_Dashboard')) {
                      <a class="dropdown-item" (click)="onClickIcon('changeManagement')" [ngClass]="iconType === 'changeManagement' ? 'active' : ''"  [routerLink]="['/change-management-dashboard']">Change Management</a>
                    }
                    @if (haveOnePmMenuAccess('App_Page','Executive_Dashboard')) {
                      <a class="dropdown-item" (click)="onClickIcon('executive')" [ngClass]="iconType === 'executive' ? 'active' : ''"  [routerLink]="['/executive-dashboard']">Executive</a>
                    }
                  </ul>
                </div>
              }
              <div class="top-nav-profile-sec">
                <a href="#" class="d-block link-light text-decoration-none dropdown-toggle" id="dropdownUser1"
                  data-bs-toggle="dropdown" aria-expanded="false">
                  <img src="{{headerConstant.user_url}}" alt="mdo" class="rounded-circle mr-2" width="32" height="32">
                  {{userName}}
                </a>
                <ul class="dropdown-menu text-small" aria-labelledby="dropdownUser1">
                  <li> <a class="dropdown-item" (click)="logoutRequst()">Sign out</a> </li>
                </ul>
              </div>
            </div>
          }
        </div>
      </header>
      <nz-breadcrumb class="from-header mb-4">
        <nz-breadcrumb-item>
          <a>{{ breadcrumbData }}</a>
        </nz-breadcrumb-item>
      </nz-breadcrumb>