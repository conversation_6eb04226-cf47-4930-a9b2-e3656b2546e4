import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { MsalInterceptor, MsalService, MsalBroadcastService, MsalGuard, MSAL_INSTANCE, MSAL_INTERCEPTOR_CONFIG, MSAL_GUARD_CONFIG } from '@azure/msal-angular';
import { MSALInstanceFactory, MSALInterceptorConfigFactory, MSALGuardConfigFactory } from './interceptors/msal.interceptor';
import { environment } from 'src/environments/environment';
import { NormalHeaderComponent } from './components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CommonModule } from '@angular/common';


import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HeaderComponent } from './components/shared-components/header/header.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { SideMenuComponent } from './components/shared-components/side-menu/side-menu.component';
import { NZ_DATE_LOCALE, NZ_I18N, en_US } from 'ng-zorro-antd/i18n';
import { enUS } from "date-fns/locale";
import { NormalComponent } from './components/shared-components/ag-renderers/cells/normal/normal.component';
import { OpportunityIdAdminComponent } from './components/shared-components/ag-renderers/cells/opportunity-id-admin/opportunity-id-admin.component';
import { CopyToClipboardDirective } from './utils/directive/copy-to-clipboard/copy-to-clipboard.directive';
import { HourComponent } from './components/shared-components/ag-renderers/cells/hour/hour.component';
import { DescriptionComponent } from './components/shared-components/ag-renderers/cells/description/description.component';
import { HashLoaderModule } from 'loader';
import { NzNotificationModule } from 'ng-zorro-antd/notification';
import { DateComponent } from './components/shared-components/ag-renderers/cells/date/date.component';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { PercentageComponent } from './components/shared-components/ag-renderers/cells/percentage/percentage.component';
import { WorklogComponent } from './components/shared-components/ag-renderers/cells/worklog/worklog.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NZ_ICONS } from 'ng-zorro-antd/icon';
import { IconDefinition } from '@ant-design/icons-angular';
import * as AllIcons from '@ant-design/icons-angular/icons';
import { JiraIdComponent } from './components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { DateInfoComponent } from './components/shared-components/ag-renderers/cells/date-info/date-info.component';
import { HourInfoComponent } from './components/shared-components/ag-renderers/cells/hour-info/hour-info.component';
import { FreshServiceComponent } from './components/shared-components/ag-renderers/cells/fresh-service/fresh-service.component';

const antDesignIcons = AllIcons as {
  [key: string]: IconDefinition;
};
const icons: IconDefinition[] = Object.keys(antDesignIcons).map(key => antDesignIcons[key])

@NgModule({ declarations: [
        AppComponent,
        HeaderComponent,
        SideMenuComponent,
        NormalHeaderComponent,
        NormalComponent,
        OpportunityIdAdminComponent,
        CopyToClipboardDirective,
        HourComponent,
        DescriptionComponent,
        DateComponent,
        PercentageComponent,
        WorklogComponent,
        JiraIdComponent,
        DateInfoComponent,
        HourInfoComponent,
        FreshServiceComponent
    ],
    bootstrap: [AppComponent], imports: [BrowserModule,
        AppRoutingModule,
        CommonModule,
        BrowserAnimationsModule,
        NzNotificationModule,
        HashLoaderModule,
        NzBreadCrumbModule,
        NzIconModule], providers: [{ provide: 'environment', useValue: environment },
        {
            provide: HTTP_INTERCEPTORS,
            useClass: MsalInterceptor,
            multi: true
        },
        { provide: MSAL_INTERCEPTOR_CONFIG, useFactory: MSALInterceptorConfigFactory },
        { provide: MSAL_INSTANCE, useFactory: MSALInstanceFactory },
        { provide: MSAL_GUARD_CONFIG, useFactory: MSALGuardConfigFactory },
        { provide: NZ_DATE_LOCALE, useValue: enUS },
        { provide: NZ_I18N, useValue: en_US },
        { provide: NZ_ICONS, useValue: icons },
        MsalService,
        MsalBroadcastService,
        MsalGuard, provideHttpClient(withInterceptorsFromDi())] })
export class AppModule { }
