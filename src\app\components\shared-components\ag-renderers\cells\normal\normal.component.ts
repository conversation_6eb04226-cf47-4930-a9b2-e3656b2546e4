import { Component } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
    selector: 'app-normal',
    templateUrl: './normal.component.html',
    styleUrls: ['./normal.component.scss'],
    standalone: false
})
export class NormalComponent implements AgRendererComponent {

  enableCopy:boolean = false;
  currCellWidth:any;
  cellValue:any;
  params: any;
  constructor() { }

  agInit(params: ICellRendererParams): void {
    this.params = params;
    let colDef:any = params.colDef
    this.enableCopy = (colDef?.enableCopy)? true : false;
    this.cellValue = this.getValueToDisplay(params)
    this.setCellWidth(params);
  }
  refresh(params: ICellRendererParams):boolean {
    this.cellValue = this.getValueToDisplay(params);
    return true
  }
  /**
   * Get Value To Display
   * @param params
   * @returns
   */
  getValueToDisplay(params: ICellRendererParams) { return params.valueFormatted ? params.valueFormatted : params.value; }
  /**
   * Set Cell Width
   * @param params
   */
  private setCellWidth(params: any): void { this.currCellWidth = params.column.actualWidth - 10; }

}
