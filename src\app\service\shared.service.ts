import { Injectable } from '@angular/core';
interface TeamMember {
  memberName: string;
  hours: number;
}
 
interface Epic {
  epicKey: string;
  teamMember: TeamMember[];
}
 
interface Project {
  projectName: string;
  epic: Epic[];
  hoursTotal: number;
}
 
interface Team {
  teamName: string;
  project: Project[];
  hoursTotal: number;
}
 
interface RowData {
  isTeam: boolean;
  team: string | null;
  isProject: boolean;
  project: string | null;
  isEpic: boolean;
  epic: string | null;
  user: string | null;
  hours: number;
  isTotal: boolean;
  total: number;
  isTeamTotal: boolean;
}
@Injectable({
  providedIn: 'root'
})
export class SharedService {
  selectedElement: HTMLElement | null = null; 
  legends:any[] = [];

  constructor() { }
  convertToFlatArray(data: Team[]): RowData[] {
    const result: RowData[] = [];
   
    // Default row template
    const defaultRow: RowData = {
      isTeam: false,
      team: null,
      isProject: false,
      project: null,
      isEpic: false,
      epic: null,
      user: null,
      hours: 0,
      isTotal: false,
      total: 0,
      isTeamTotal: false
    };
   
    data.forEach(teamData => {
      const teamName = teamData.teamName;
   
      // Add team row
      result.push({
        ...defaultRow,
        isTeam: true,
        team: teamName,
        hours: 0
      });
   
      teamData.project.forEach(projectData => {
        const projectName = projectData.projectName;
   
        // Add project row
        result.push({
          ...defaultRow,
          isProject: true,
          project: projectName
        });
   
        projectData.epic.forEach(epicData => {
          const epicKey = epicData.epicKey;
   
          // Add epic row
          result.push({
            ...defaultRow,
            isEpic: true,
            epic: epicKey
          });
   
          // Add team member rows
          epicData.teamMember.forEach(member => {
            result.push({
              ...defaultRow,
              team: teamName,
              project: projectName,
              epic: epicKey,
              user: member.memberName,
              hours: member.hours
            });
          });
        });
   
        // Add project total row
        result.push({
          ...defaultRow,
          isTotal: true,
          team: `${projectName} Total`,
          total: projectData.hoursTotal
        });
      });
   
      // Add team total row
      result.push({
        ...defaultRow,
        isTeamTotal: true,
        team: `${teamName} Total`,
        total: teamData.hoursTotal
      });
    });
   
    return result;
  }
}
