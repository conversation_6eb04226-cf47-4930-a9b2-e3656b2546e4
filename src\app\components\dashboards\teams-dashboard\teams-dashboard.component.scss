.card-title {
    padding: 8px 0px 0px !important;
}
 
.card-title {
    width: 100% !important;
    text-overflow: ellipsis;
    overflow: hidden;
    text-wrap: nowrap;
}
 
.customize-header {
    min-height: 35px !important;
    padding: 0 10px !important;
    background-color: #59AFFD;
    color: #fff !important;
    border-radius: 6px 6px 0 0 !important;
    font-size: 13px;
}
 
.card-body {
    padding: 9px;
    background-color: white;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}
 
.delete-btn {
    color: #538abc;
    cursor: pointer;
    border: none;
    background: transparent;
    float: right;
    font-size: 15px;
}
 
.delete-btn:hover {
    color: darkred;
}
 
.empty-list-placeholder {
    color: #999;
    min-height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 10px;
}
 
.tag {
    display: flex;
    align-items: center;
    background-color: #e0e0e0;
    border-radius: 5px;
    padding: 2px 15px 2px 12px;
    color: #333;
    position: relative;
    line-height: 1;
    margin-right: 7px;
    margin-bottom: 7px;
    font-size: 12px;
}
 
.tag-icon {
    margin-right: 5px;
}
 
.blue {
    background: #E2F0FF;
    border: 1px solid #007BFF;
    color: #007bff;
    cursor: pointer;
}
 
.teal {
    background: #D9FFFB;
    border: 1px solid #02AB97;
    color: #02AB97;
    cursor: pointer;
}
 
.white {
    background-color: #fff;
    border: 1px solid #000;
    color: #000;
    cursor: pointer;
}
 
.add-job-btn {
    float: right;
    font-size: 10px;
    padding: 0px 7px;
}
 
.team-details {
    height: calc((100dvh/2) - 158px);
    overflow-y: auto;
    scrollbar-width: thin;
}
 
.list-group {
    border-radius: 0;
 
    .list-group-item {
        padding: 3px 1px 3px 4px;
        background: #F1F1F1;
        margin-bottom: 3px;
        border-radius: 3px;
    }
}
 
.accordion-body {
    padding: 0px !important;
}
 
.member-cursor,
.project-cursor,
.internal-teams-cursor {
    cursor: pointer;
}
 
.job-type-title,
.project-name,
.project-priority,
.team-member-role,
.table {
    font-size: 12px;
}
 
.display-flex {
    display: flex;
 
    .title {
        font-weight: 550;
        margin-bottom: 0;
        font-size: 12px;
        margin: 0;
        color: #555;
    }
}
 
.list-group-item.active {
    background-color: #408bc0 !important;
    color: white !important;
    border-color: #408bc0 !important;
}
 
.member-section,
.project-section,
.job-drop-section,
.internal-teams-section {
    height: 50%;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: 6px;
    padding: 3px;
    background-color: #ffffff;
}
 
#accordionSubTeams {
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: 6px;
    padding: 4px;
    background-color: #ebebeb;
}
 
tbody,
td,
tfoot,
th,
thead,
tr {
    padding: 2px !important;
}
 
.tag-container {
    display: flex;
    gap: 10px;
    flex-direction: row;
    flex-wrap: wrap;
}
 
.selected {
    color: #333;
    border: 2px solid;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), 0 0 15px rgba(0, 0, 0, 0.1);
}
 
.badge {
    background-color: red;
    color: white;
    border-radius: 50%;
    padding: 3px 6px;
    font-size: 10px;
    position: absolute;
    top: -5px;
    right: -10px;
}
 
.team-member-role {
    float: right;
}
 
.icon {
    transition: transform 0.3s ease;
    margin-left: 5px;
}
 
.icon i {
    font-weight: 300;
    font-size: 10px;
}
.project-drop-section {
    max-height: 200px; /* Adjust based on your design */
    overflow-y: auto;
    position: relative;
  }
 
  .cdk-drag-preview {
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
                0 8px 10px 1px rgba(0, 0, 0, 0.14),
                0 3px 14px 2px rgba(0, 0, 0, 0.12);
    opacity: 0.8;
  }
 
  .cdk-drag-placeholder {
    opacity: 0.3;
    background: #ccc;
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
 
  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
 
  tbody.cdk-drop-list-dragging .cdk-drag {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
  .ico-align{
    padding-top: 4px;
    font-size: 10px;
  }
  .save-btn-project{
    background-color: #1890ff;
  }
  .font-size-custom{
    font-size: 12px;
  }
  .project-custom-padding{
    margin-top: -8px;
  }