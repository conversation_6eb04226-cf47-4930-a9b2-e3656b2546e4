import { Component, OnInit } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-fresh-service',
    templateUrl: './fresh-service.component.html',
    styleUrls: ['./fresh-service.component.scss'],
    standalone: false
})
export class FreshServiceComponent implements AgRendererComponent {
  linkValue:string = "";
  cellValue:any;
  freshTicketId!:number;
  constructor() { }
   agInit(params: ICellRendererParams): void {
      this.cellValue = this.getValueToDisplay(params);
      this.freshTicketId = this.cellValue?.replace(/\D/g, '');
      this.linkValue = `${environment.freshServiceUrl}${this.freshTicketId}`;    
    }
  
    refresh(params: ICellRendererParams):boolean {
      this.cellValue = this.getValueToDisplay(params);
      return true
    }
    handleClick(event:any){
      window.open(this.linkValue, '_blank');
    }
    getValueToDisplay(params: ICellRendererParams) { return params.valueFormatted ? params.valueFormatted : params.value; }

}
