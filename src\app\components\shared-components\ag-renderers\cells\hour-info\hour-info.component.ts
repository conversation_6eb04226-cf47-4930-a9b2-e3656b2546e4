import { Component, OnInit } from '@angular/core';
import { AgRendererComponent } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
    selector: 'app-hour-info',
    templateUrl: './hour-info.component.html',
    styleUrls: ['./hour-info.component.scss'],
    standalone: false
})
export class HourInfoComponent implements AgRendererComponent  {

 cellValue:any;
  agInit(params: ICellRendererParams): void {     
    this.cellValue = params.value.hours;
   }
  refresh(params: ICellRendererParams):boolean {
    this.cellValue =params.value.hours;
    return true
  }

}
