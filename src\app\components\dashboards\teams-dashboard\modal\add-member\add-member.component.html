<div class="container-fluid">
  <form [formGroup]="memberForm">
    <div class="row mb-2">
      <label for="title" class="mb-0 col-sm-5 col-form-label">Member <span class="text-danger">*</span></label>
      <div class="col-sm-7">
        <select class="form-select form-control custom-dropdown" formControlName="memberId">
          <option  value="null" disabled>Select Member</option>
          @for (member of memberData; track member) {
            <option value="{{member.key}}" >{{member.name}}</option>
          }
        </select>
      </div>
    </div>
    <div class="row mb-2">
      <label for="description" class="mb-0 col-sm-5 col-form-label">Category <span class="text-danger">*</span></label>
      <div class="col-sm-7">
        <select class="form-select form-control custom-dropdown"  formControlName="category">
          <option  value="null" disabled>Select Category</option>
          @for (category of categoryData; track category) {
            <option value="{{category.value}}" >{{category.value}}</option>
          }
        </select>
      </div>
    </div>
    <div class="row">
      <label for="description" class="mb-0 col-sm-5 col-form-label">Skill <span class="text-danger">*</span></label>
      <div class="col-sm-7">
        <input type="text" class="form-control" id="title" formControlName="skill"
          placeholder="Enter Skill" name="title" autocomplete="off"  />
        </div>
      </div>
    </form>
  </div>
  <ng-template #modalFooterTemplate let-modal>
    <button type="reset" nz-button nzType="default" class="me-1" (click)="onReset()"> <span><i class="fa fa-refresh fa-solid me-2"></i></span> Reset</button>
    <button type="submit" nz-button nzType="primary" class="custom-pad-btn" (click)="onSubmit()" [disabled]="memberForm.invalid">
      <span><i class="fa fa-floppy-o fa-solid me-2"></i></span>Save
    </button>
  </ng-template>