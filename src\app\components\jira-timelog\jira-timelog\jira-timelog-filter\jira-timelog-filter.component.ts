import { Component, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { IDropdownSettings, MultiSelectComponent } from 'ng-multiselect-dropdown';
import { LoaderService } from 'loader';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { environment } from 'src/environments/environment';
import moment from 'moment';


@Component({
    selector: 'app-jira-timelog-filter',
    templateUrl: './jira-timelog-filter.component.html',
    styleUrls: ['./jira-timelog-filter.component.scss'],
    standalone: false
})

export class JiraTimelogFilterComponent implements OnInit {
  @ViewChildren('teams') dropdowns!: QueryList<MultiSelectComponent>;
  timeLogForm!: FormGroup;
  teamsData: any[] = [];
  @Input() excelData: any;
  @Output() emitFilters = new EventEmitter<any>();
  assetURL = environment.appBaseURL;

  constructor(private formBuilder: UntypedFormBuilder,private onePmService: OnepmServiceService, private loaderService: LoaderService, private notification: NzNotificationService, ) { }

  ngOnInit(): void {
    this.initialForm();
    this.getMasterData();
  }
  /**
   * Function is used to call Master data API and set to Teams Dropdown data 
   */
  getMasterData() {
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getTimeLogMasterData().subscribe((res: any) => {
      if (res.succeeded) {
        this.loaderService.invokeLoaderComponent(false);
        this.teamsData = res.data.team; 
        // this.timeLogForm.controls['teams'].setValue([this.teamsData[0]]);
        // this.onSearch(false);       
      }
      else {
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    })
  }
   /**
   * Function to change event of date picker 
   */
   onOpenChange(isOpen: boolean): void {
    if (isOpen) this.closeAllDropdowns();
  }

  /**
   * Function to close ng select dropdowns  while opening date picker
   */
  closeAllDropdowns(): void {
    this.dropdowns.forEach((dropdown) => {
      dropdown.closeDropdown();
    });
  }

  /**
      * Function to set ng multiselect filter settings
      */
    filterSettings(
      singleSelection: boolean,
      enableCheckAll: boolean,
      allowSearchFilter: boolean,
      id: string,
      name: string): IDropdownSettings {
      return ({
        singleSelection: singleSelection,
        idField: id,
        textField: name,
        selectAllText: 'All',
        unSelectAllText: 'All',
        itemsShowLimit: 1,
        enableCheckAll: enableCheckAll,
        allowSearchFilter: allowSearchFilter,
      });
    }
/**
   * Function to initialize form
   */
    initialForm(): FormGroup {
        return this.timeLogForm = this.formBuilder.group({
          teams: [null, Validators.required],
          startDate: [this.getCurrentWeekDates().monday,Validators.required],
          endDate: [this.getCurrentWeekDates().friday,Validators.required]
        });
      }

       /**
   * Function to get Current week date (monday and friday for initializing default)
  */
  getCurrentWeekDates(): { monday: Date; friday: Date } {
    const today = new Date();
    const dayOfWeek = today.getDay(); 
    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const diffToFriday = dayOfWeek === 0 ? -2 : 5 - dayOfWeek;
    const monday = new Date(today);
    const friday = new Date(today);
    monday.setDate(today.getDate() + diffToMonday);
    friday.setDate(today.getDate() + diffToFriday);
    return { monday, friday };
  }
/**
   * Function to Change date format 
   * @param value Contain date
   */
  changeDateFormats(value: Date) {
    return moment(value).format('YYYY-MM-DD')
  }
  /**
   * Disables dates in the 'To Date' picker before the selected 'From Date'.
   * @param current Date to be checked.
   * @returns Boolean indicating whether the date should be disabled.
   */
    disableToDate = (current: Date): boolean => {
      const fromDate = this.timeLogForm.get('startDate')?.value;
      return fromDate ? moment(current).isBefore(moment(fromDate), 'day') : false;
    };
     /**
     * Disables dates in the 'From Date' picker after the selected 'To Date'.
     * @param current Date to be checked.
     * @returns Boolean indicating whether the date should be disabled.
     */
    disableFromDate = (current: Date): boolean => {
      const toDate = this.timeLogForm.get('endDate')?.value;
      return toDate ? moment(current).isAfter(moment(toDate), 'day') : false;
    };
  /**
   * Function to emit filter data to parent component
   * @param clear 
   */
  onSearch(clear?: boolean){
    if (this.timeLogForm.valid) {
      const startDate = this.changeDateFormats(this.timeLogForm.controls['startDate'].value);
      const endDate = this.changeDateFormats(this.timeLogForm.controls['endDate'].value);
      const selectedItem = this.timeLogForm.controls['teams'].value?.map((item: any) => item['name']).join(',');
      const payloadData = {
        teams: selectedItem ? selectedItem : null,
        startDate: startDate,
        endDate: endDate
      }
      this.emitFilters.emit({ data: payloadData, clearFilter: clear });
    } else { 
      if(!this.timeLogForm.controls['startDate'].value){
        this.notification.blank('', 'Please select From Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      if(!this.timeLogForm.controls['endDate'].value){
        this.notification.blank('', 'Please select To Date', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
      if(!this.timeLogForm.controls['teams'].value || this.timeLogForm.controls['teams'].value.length === 0){
        this.notification.blank('', 'Please select at least one Team', { nzPlacement: 'bottomLeft', nzClass: 'error' }); 
      }
    }
  }
 /**
   * Event of to Clear Button
   */
 onClearFilter() {
  this.timeLogForm.reset();
  // this.onSearch(true);
}


}
