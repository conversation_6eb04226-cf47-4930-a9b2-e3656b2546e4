import { Component, OnInit } from '@angular/core';
import { OnepmServiceService } from 'src/app/service/onepm-service.service';
import { LoaderService } from 'loader';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import moment from 'moment';

@Component({
    selector: 'app-ba-reporting-dashboard',
    templateUrl: './ba-reporting-dashboard.component.html',
    styleUrls: ['./ba-reporting-dashboard.component.scss'],
    standalone: false
})
/*
  This Component is a parent Component,and combination of Both EOD Reporting and PMO EOD Reporting
 */ 
export class BaReportingDashboardComponent implements OnInit {
  gridMode: string = '';
  fromDate: string = '';
  toDate: string = '';
  pmoEodData: any = [];
  allApplicationBugs: any[] = [];
  activeChangeManagementRequests: any[] = [];
  salesforceCases: any[] = [];
  salesForceSupportCases: any[] = [];
  currentlyOpenCases:any[] = [];
  currentlyOpenColDefs:any;
  closedCases:any[] = [];
  closedColDefs:any;
  openedTodayCases:any[] = [];
  oneButtonPriorityData:any[] = [];
  isApplicationBugLoader: boolean = false;
  finalLoader: boolean = false;
  oneButtonTotalContractsToDate: number = 0;
  contractCreationTraditionalMetrics:any;
  oneButtonData: any;
  oneDrawData: any;
  baStatusUpdateData: any;
  openedTodayColDefs:any;
  oneDrawColDef = dashboardConstants.oneDrawColDef;
  constructor(
    private onePmService: OnepmServiceService,
    private loaderService: LoaderService,
    private notification: NzNotificationService
  ) {}

  ngOnInit(): void {}
  /**
   * Handles filter changes and updates the grid data accordingly.
   * @param event Contains filter details and whether to clear filters.
   */
  getFilterChange(event: any) {
    if (event.clearFilter) {
      this.gridMode = '';
      return;
    }
    this.gridMode = event.data.report;
    this.fromDate = event.data.date;
    // this.toDate = event.data.toDate;
    if (this.gridMode == 'PMO_EOD_REPORTING') {
      this.getPmoEodData();
    }else if(this.gridMode == 'EOD_REPORTING'){
      this.updateColdef();
      this.getEodData();
      this.getEodDataRemaining();
    }
  }
  /**
   * Converts a date string from "YYYY-MM-DD" format to "MM/DD/YYYY".
   * @param dateStr The date string to convert.
   * @returns The formatted date string.
   */
  convertDateFormat(dateStr: string): string {
    if (!dateStr) return ''; // Handle empty or invalid input
    const [year, month, day] = dateStr.split('-');
    return `${month}-${day}-${year}`;
  }
  /**
    * Method to  call API to get PMO EOD Data
  */
  getPmoEodData() {
    let data = {
      date: this.fromDate,
      // endDate: this.toDate,
    };
    this.pmoEodData = [];
    this.loaderService.invokeLoaderComponent(true);
    this.onePmService.getPmoEodData(data).subscribe((res: any) => {
      if (res.succeeded) {
        this.pmoEodData = res.data.teams;
        this.loaderService.invokeLoaderComponent(false);
      } else {
        this.pmoEodData = [];
        this.loaderService.invokeLoaderComponent(false);
        this.notification.blank('', res.message, {
          nzPlacement: 'bottomLeft',
          nzClass: 'error',
        });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
      this.pmoEodData = [];
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    });
  }
 /**
    * Method to  call API to get EOD Data
  */
  getEodData() {
    let data = {
      date: this.fromDate,
    };
    this.isApplicationBugLoader = true;
    this.allApplicationBugs = [''];
    this.activeChangeManagementRequests = [''];
    this.onePmService.getEodDataInitial(data).subscribe((res: any) => {
      if (res.succeeded) {
        this.isApplicationBugLoader = false;
        this.allApplicationBugs = res.data.allApplicationBugs;   
        this.activeChangeManagementRequests = res.data.activeChangeManagementRequests;
      } else {
        this.activeChangeManagementRequests = [];
        this.isApplicationBugLoader = false;
        this.notification.blank('', res.message, {
          nzPlacement: 'bottomLeft',
          nzClass: 'error',
        });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
        this.isApplicationBugLoader = false;
      this.activeChangeManagementRequests = [];
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    });
  }
  /**
    * Method to  call API to get EOD Data Remaining
  */
  getEodDataRemaining(){
    let data = {
      date: this.fromDate,
    };    
    this.salesforceCases = [''];
    this.salesForceSupportCases = [''];
    this.oneDrawData = [''];
    this.baStatusUpdateData = [''];
    this.oneButtonPriorityData = [''];
    this.currentlyOpenCases = [''];
    this.openedTodayCases = [''];
    this.closedCases = [''];
    this.finalLoader = true;
    this.onePmService.getEodDataRemaining(data).subscribe((res: any) => {
      if (res.succeeded) {
        this.finalLoader = false;        
        this.salesforceCases = res.data.salesforceCases.developmentPriorities;   
        this.salesForceSupportCases = res.data.salesforceCases.supportPriorities;
        this.oneButtonTotalContractsToDate  = res.data.oneButton.contractCreationTraditionalMetrics.totalContractsToDate;
        this.oneButtonData = res.data.oneButton;
        this.oneButtonPriorityData = this.oneButtonData.developmentPriorities;
        let oneDrawData = res.data.oneDraw;
        this.oneDrawData = this.convertDataToRowData(oneDrawData);
        this.baStatusUpdateData = res.data.baStatusUpdate;
        let currentlyOpenCases = res.data.dailySupportCasesInfo.currentlyOpen;
        ({ rowData: this.currentlyOpenCases, columnDefs: this.currentlyOpenColDefs } = this.buildDynamicGrid(currentlyOpenCases));
        let closedCases = res.data.dailySupportCasesInfo.closedToday;
        ({ rowData: this.closedCases, columnDefs: this.closedColDefs } = this.buildDynamicGrid(closedCases));
        let openCases = res.data.dailySupportCasesInfo.openedToday;
        ({ rowData:this.openedTodayCases, columnDefs: this.openedTodayColDefs } = this.buildDynamicGrid(openCases));
      } else {
        this.salesforceCases = [];        
        this.salesForceSupportCases = [];
        this.finalLoader = false;        
        this.notification.blank('', res.message, {
          nzPlacement: 'bottomLeft',
          nzClass: 'error',
        });
      }
    }, (err) => {
      this.loaderService.invokeLoaderComponent(false);
        this.salesforceCases = [];      
        this.salesForceSupportCases = [];
        this.finalLoader = false;        
      this.notification.blank('', err.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
    });
  }
  /**
    * Method to covert to row basis
    * * @param data - oneDraw Api response data 
  */
  convertDataToRowData(data: { [key: string]: number }): { key: string, count: number }[] {
  const labelMap: { [key: string]: string } = {
    totalReportedBugs: 'Total Reported Bugs',
    totalBugsClosed: 'Total Bugs Closed',
    bugTicketsInProgress: 'Bug Tickets in Progress',
    bugTicketsYetToBeStarted: 'Bug Tickets Yet to be started',
    totalOpenBugTickets: 'Total Open Bug Tickets',
  };

  return Object.keys(data).map((key) => ({
    key: labelMap[key] || key,
    count: data[key],
  }));
  }
  /**
    * Method to create dynamic data
    * * @param originalData - dynamic grid Api response data 
  */
  buildDynamicGrid(originalData: any[]) {
  const agentSet = new Set<string>();
  originalData.forEach((item: any) => {
    item.agents.forEach((agent: any) => {
      agentSet.add(agent.agentName);
    });
  });
  const agentList = Array.from(agentSet).sort();
  const rowData = originalData.map((item: any) => {
    const row: any = {
      Application: item.applicationName,
      TOTAL: 0
    };
    agentList.forEach(agent => {
      const agentData = item.agents.find((a: any) => a.agentName === agent);
      const count = agentData ? agentData.ticketCount : 0;
      row[agent] = count;
      row.TOTAL += count;
    });
    return row;
  });
  const columnDefs = [
    { headerName: 'Application', field: 'Application',flex:1 },
    ...agentList.map(agent => ({
      headerName: agent,
      field: agent,
      flex:1
    })),
    { headerName: 'Total', field: 'TOTAL',flex:1 }
  ];
  return { rowData, columnDefs };
  }
  /**
    * Method to create dynamic grid header name 
  */
  updateColdef() {
  this.oneDrawColDef = this.oneDrawColDef.map((s: any) => {
    if (s.field === 'key') {
      return {
        ...s,
        headerName: `Key Open Issues Report ${moment(this.fromDate).format('MM/DD/YYYY')}`
      };
    }
    return s;
  });
  }
}
