<div id="onepm-form" [formGroup]="filterForm">
    <div class="row mt-4">
        <div class="col-lg-2 col-md-3 section-item">
            <label class="multiSelectLabel ml-4">Group</label>
            <select class="form-select form-control custom-dropdown" formControlName="view">
                <option value=null disabled>Select</option>                
                <option *ngFor="let item of viewData" value="{{item.key}}">{{ item.value }}</option>
            </select>
        </div>
        <div class="col-lg-2 col-md-2 section-item section-first-item custom-date-width">
            <label class="multiSelectLabel ml-4">From Date</label>
            <nz-date-picker id="forms-due-date-from" nzFormat="MM-dd-yyyy" formControlName="startDate"
                class="custom-input w-100 date-height" [nzDisabledDate]="disableFromDate">
            </nz-date-picker>
        </div>
        <div class="col-lg-2 col-md-2 section-item custom-date-width">
            <label class="multiSelectLabel ml-4">To Date</label>
            <nz-date-picker id="forms-due-date-to" nzFormat="MM-dd-yyyy" formControlName="endDate"
                class="custom-input w-100 date-height" [nzDisabledDate]="disableToDate">
            </nz-date-picker>
        </div>
        <div class="col-lg-3 col-md-2 section-item ">
            <div class="search-btn">
                <button type="button" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary" title="Search"
                    (click)="onSearch()">
                    <i aria-hidden="true" class="fa fa-search"></i></button>
                <button type="button" id="reset-btn" class="ant-btn ant-btn-primary me-2" nz-button nzType="primary"
                    title="Clear" (click)="onClear()">
                    <img [src]="assetURL + '/assets/images/clear-search-icon.svg'" height="15px">
                </button>
            </div>
        </div>
        <div class="col-lg-3 col-md-3 justify-content-end ms-auto">
            <div class="search-btn float-end">
                <div class="excel-btn">
                    <button type="button" class="ant-btn btn btn-success me-2" (click)="onExport()" [disabled]="!(excelData.length > 0)">
                        <img [src]="assetURL +'/assets/images/Excel-Icon.png'">
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>