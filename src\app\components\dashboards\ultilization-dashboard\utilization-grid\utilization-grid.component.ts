import { Component, OnInit, Input } from '@angular/core';
import { DateInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/date-info/date-info.component';
import { DateComponent } from 'src/app/components/shared-components/ag-renderers/cells/date/date.component';
import { DescriptionComponent } from 'src/app/components/shared-components/ag-renderers/cells/description/description.component';
import { HourInfoComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour-info/hour-info.component';
import { HourComponent } from 'src/app/components/shared-components/ag-renderers/cells/hour/hour.component';
import { JiraIdComponent } from 'src/app/components/shared-components/ag-renderers/cells/jira-id/jira-id.component';
import { NormalComponent } from 'src/app/components/shared-components/ag-renderers/cells/normal/normal.component';
import { PercentageComponent } from 'src/app/components/shared-components/ag-renderers/cells/percentage/percentage.component';
import { NormalHeaderComponent } from 'src/app/components/shared-components/ag-renderers/headers/normal-header/normal-header.component';
import { dashboardConstants } from 'src/app/utils/apps-constants';
import * as Highcharts from 'highcharts';
import { UtlizationChartData } from 'src/app/utils/apps.interface';
import { onepmApi } from 'src/app/utils/sample-data';

@Component({
    selector: 'app-utilization-grid',
    templateUrl: './utilization-grid.component.html',
    styleUrls: ['./utilization-grid.component.scss'],
    standalone: false
})
/* 
  Component for Utilization Grid, child of Utilization Dashboard
*/
export class UtilizationGridComponent implements OnInit {
  gridStyle: string = dashboardConstants.gridStyle;
  noRowsTemplate = dashboardConstants.noRowsTemplate;
  defaultColDef = dashboardConstants.defaultColDef;
  gridOptions = dashboardConstants.gridOptions;
  @Input() columnDefs: any[] = [];
  @Input() rowData: any[] = [];

  gridApi: any;
  @Input() gridData: any[] = [];
  @Input() chartData!: UtlizationChartData;
  @Input() hoursandCountData!: any;
  chartOptions!: Highcharts.Options;
  isDivVisible: any;
  Highcharts: typeof Highcharts = Highcharts;
  chartlabel: string[] = [];
  capacityListData: number[] = [];
  utilizationListdata: any[] = [];

  constructor() {}

  // Component registry for ag-grid v30+
  components = {
    normalHeader: NormalHeaderComponent,
    normalRenderer: NormalComponent,
    hourRenderer: HourComponent,
    descRenderer: DescriptionComponent,
    dateRenderer: DateComponent,
    percentageRenderer: PercentageComponent,
    jiraIdRenderer: JiraIdComponent,
    dateInfoRenderer: DateInfoComponent,
    hourInfoRenderer: HourInfoComponent,
  };

  ngOnInit(): void {
    this.onChartReady();
  }

  ngOnChanges() {
    this.chartlabel = [];
    this.capacityListData = [];
    this.utilizationListdata = [];
    this.isDivVisible = false;
    if (this.chartData) {
      const { employeeName, capacity, mtdHours } = this.chartData;
      if (employeeName?.length) {
        this.chartlabel = employeeName;
      }
      if (capacity?.length) {
        this.capacityListData = capacity;
      }
      if (mtdHours?.length) {
        this.utilizationListdata = mtdHours;
      }
      if (employeeName?.length && capacity?.length && mtdHours?.length) {
        this.onChartReady();
      }
    } else {
      this.chartlabel = [];
      this.capacityListData = [];
      this.utilizationListdata = [];
      this.onChartReady();
    }
  }

  /**
   * Function initiate highchart for column chart
   */
  onChartReady() {
    this.chartOptions = {
      chart: {
        type: 'column',
      },
      title: {
        text: 'Capacity vs. MTD Hours',
      },
      xAxis: {
        categories: [...this.chartlabel],
      },
      yAxis: {
        min: 0,
        title: {
          text: 'Hours',
        },
      },
      series: [
        {
          name: 'Capacity',
          type: 'column',
          data: [...this.capacityListData], // Capacity (Gray Bars)
          color: 'gray',
          opacity: 0.5,
        },
        {
          name: 'MTD Hours',
          type: 'column',
          data: [...this.utilizationListdata],
        },
      ],
      plotOptions: {
        column: {
          dataLabels: {
            enabled: true,
            color: '#000',
            style: {
              fontWeight: 'bold',
            },
          },
        },
      },
    };
  }

  /**
   * Adjusts column sizes dynamically when the window resizes.
   * @param param 
   */
  updateColumnSize(param: any) {
    window.addEventListener('resize', () => {
      setTimeout(() => param.sizeColumnsToFit(), 500);
    });
  }

  /**
   * Hides the AG-Grid overlay after a short delay.
   */
  showBlankOverlay() {
    setTimeout(() => {
      this.gridApi?.hideOverlay();
    }, 1);
  }
  /**
   * AG-Grid initialization when the grid is ready.
   * @param params 
   */
  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.hideOverlay();
    this.updateColumnSize(this.gridApi);
  }
  /*
    Event for hide or show Chart icon
  */
  toggleDiv() {
    this.isDivVisible = !this.isDivVisible;
  }
}
